-- RPC Functions for Categories Management
-- Run this script in Supabase SQL Editor

-- 1. Function to get all categories with counts
CREATE OR REPLACE FUNCTION get_categories()
RETURNS JSON AS $$
DECLARE
  result JSON;
BEGIN
  SELECT json_agg(
    json_build_object(
      'id', category,
      'name', category,
      'description', '<PERSON>h mục ' || category,
      'slug', LOWER(REPLACE(category, ' ', '-')),
      'created_at', NOW(),
      'updated_at', NOW(),
      'count', article_count
    )
  )
  INTO result
  FROM (
    SELECT 
      category,
      COUNT(*) as article_count
    FROM news_articles 
    WHERE category IS NOT NULL 
      AND category != '' 
      AND is_published = true
    GROUP BY category
    ORDER BY article_count DESC, category ASC
  ) categories;
  
  RETURN COALESCE(result, '[]'::JSON);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 2. Function to add a new category
CREATE OR REPLACE FUNCTION add_category(category_name TEXT)
RETURNS JSON AS $$
DECLARE
  existing_count INTEGER;
  result JSON;
BEGIN
  -- Check if category already exists
  SELECT COUNT(*) INTO existing_count
  FROM news_articles 
  WHERE category = category_name;
  
  IF existing_count > 0 THEN
    RAISE EXCEPTION 'Category already exists: %', category_name;
  END IF;
  
  -- Create a dummy article with this category to register it
  -- (This is a workaround since we don't have a separate categories table)
  INSERT INTO news_articles (
    title,
    content,
    excerpt,
    category,
    is_published,
    is_featured,
    author,
    slug,
    publish_date
  ) VALUES (
    '__CATEGORY_PLACEHOLDER__' || category_name,
    'This is a placeholder article to register the category: ' || category_name,
    'Category placeholder',
    category_name,
    false, -- Not published so it won't show up
    false,
    'System',
    '__category-placeholder-' || LOWER(REPLACE(category_name, ' ', '-')),
    NOW()
  );
  
  -- Return success response
  SELECT json_build_object(
    'id', category_name,
    'name', category_name,
    'description', 'Danh mục ' || category_name,
    'slug', LOWER(REPLACE(category_name, ' ', '-')),
    'created_at', NOW(),
    'updated_at', NOW(),
    'count', 0
  ) INTO result;
  
  RETURN result;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 3. Function to delete a category
CREATE OR REPLACE FUNCTION delete_category(category_name TEXT)
RETURNS JSON AS $$
DECLARE
  article_count INTEGER;
  result JSON;
BEGIN
  -- Check if any published articles use this category
  SELECT COUNT(*) INTO article_count
  FROM news_articles 
  WHERE category = category_name 
    AND is_published = true;
  
  IF article_count > 0 THEN
    RAISE EXCEPTION 'Cannot delete category "%" because % published articles are using it', category_name, article_count;
  END IF;
  
  -- Delete placeholder articles for this category
  DELETE FROM news_articles 
  WHERE category = category_name 
    AND title LIKE '__CATEGORY_PLACEHOLDER__%'
    AND is_published = false;
  
  -- Return success response
  SELECT json_build_object(
    'success', true,
    'message', 'Category deleted successfully',
    'category', category_name
  ) INTO result;
  
  RETURN result;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 4. Function to get categories for dropdown (including placeholder categories)
CREATE OR REPLACE FUNCTION get_all_categories()
RETURNS JSON AS $$
DECLARE
  result JSON;
BEGIN
  SELECT json_agg(DISTINCT category)
  INTO result
  FROM news_articles 
  WHERE category IS NOT NULL 
    AND category != ''
  ORDER BY category ASC;
  
  RETURN COALESCE(result, '[]'::JSON);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 5. Function to check if category exists
CREATE OR REPLACE FUNCTION category_exists(category_name TEXT)
RETURNS BOOLEAN AS $$
DECLARE
  exists_count INTEGER;
BEGIN
  SELECT COUNT(*) INTO exists_count
  FROM news_articles 
  WHERE category = category_name;
  
  RETURN exists_count > 0;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permissions to authenticated users
GRANT EXECUTE ON FUNCTION get_categories() TO authenticated;
GRANT EXECUTE ON FUNCTION add_category(TEXT) TO authenticated;
GRANT EXECUTE ON FUNCTION delete_category(TEXT) TO authenticated;
GRANT EXECUTE ON FUNCTION get_all_categories() TO authenticated;
GRANT EXECUTE ON FUNCTION category_exists(TEXT) TO authenticated;

-- Grant execute permissions to anon users for read-only functions
GRANT EXECUTE ON FUNCTION get_categories() TO anon;
GRANT EXECUTE ON FUNCTION get_all_categories() TO anon;
GRANT EXECUTE ON FUNCTION category_exists(TEXT) TO anon;
