-- Create categories table
CREATE TABLE IF NOT EXISTS categories (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  name VARCHAR(255) NOT NULL UNIQUE,
  description TEXT,
  slug VARCHAR(255) NOT NULL UNIQUE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_categories_name ON categories(name);
CREATE INDEX IF NOT EXISTS idx_categories_slug ON categories(slug);

-- Create RLS policies
ALTER TABLE categories ENABLE ROW LEVEL SECURITY;

-- Allow public to read categories
CREATE POLICY "Allow public to read categories" ON categories
  FOR SELECT USING (true);

-- Allow authenticated users to manage categories (for admin)
CREATE POLICY "Allow authenticated users to manage categories" ON categories
  FOR ALL USING (auth.role() = 'authenticated');

-- Insert default categories from existing news articles
INSERT INTO categories (name, slug, description) 
SELECT DISTINCT 
  category as name,
  LOWER(REPLACE(REPLACE(category, ' ', '-'), 'ã', 'a')) as slug,
  'Danh mục ' || category as description
FROM news_articles 
WHERE category IS NOT NULL AND category != ''
ON CONFLICT (name) DO NOTHING;

-- Function to generate slug from name
CREATE OR REPLACE FUNCTION generate_category_slug(category_name TEXT)
RETURNS TEXT AS $$
BEGIN
  RETURN LOWER(
    REPLACE(
      REPLACE(
        REPLACE(
          REPLACE(
            REPLACE(
              REPLACE(
                REPLACE(
                  REPLACE(category_name, 'ã', 'a'),
                  'ă', 'a'
                ),
                'â', 'a'
              ),
              'ê', 'e'
            ),
            'ô', 'o'
          ),
          'ơ', 'o'
        ),
        'ư', 'u'
      ),
      ' ', '-'
    )
  );
END;
$$ LANGUAGE plpgsql;

-- Function to get category count
CREATE OR REPLACE FUNCTION get_category_count(category_name TEXT)
RETURNS INTEGER AS $$
BEGIN
  RETURN (
    SELECT COUNT(*)
    FROM news_articles 
    WHERE category = category_name AND is_published = true
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
