import { useState, useEffect } from "react";
import { supabase } from "@/integrations/supabase/client";

const CATEGORIES_STORAGE_KEY = "bicom_custom_categories";

export interface Category {
  id: string;
  name: string;
  description?: string;
  slug: string;
  created_at: string;
  updated_at: string;
}

export interface CategoryWithCount extends Category {
  count: number;
}

export interface NewCategory {
  name: string;
  description?: string;
}

export const useCategories = () => {
  const [categories, setCategories] = useState<CategoryWithCount[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchCategories = async () => {
    try {
      setLoading(true);
      setError(null);

      // Use RPC function to get categories with counts
      const { data: categoriesData, error: rpcError } = await (
        supabase as any
      ).rpc("get_categories");

      if (rpcError) {
        console.error("RPC Error:", rpcError);
        // Fallback to manual calculation if RPC fails
        const { data: articles } = await (supabase as any)
          .from("news_articles")
          .select("category")
          .not("category", "is", null);

        const articleCategories = articles
          ? [
              ...new Set(
                articles
                  .map((a: any) => a.category)
                  .filter((c: string) => c && c.trim())
              ),
            ]
          : [];

        const categoriesWithCounts = await Promise.all(
          articleCategories.map(async (categoryName: string) => {
            const { data: articlesData } = await (supabase as any)
              .from("news_articles")
              .select("id")
              .eq("category", categoryName)
              .eq("is_published", true);

            return {
              id: categoryName,
              name: categoryName,
              description: `Danh mục ${categoryName}`,
              slug: categoryName.toLowerCase().replace(/[^a-z0-9]/g, "-"),
              created_at: new Date().toISOString(),
              updated_at: new Date().toISOString(),
              count: articlesData?.length || 0,
            };
          })
        );

        categoriesWithCounts.sort((a, b) => {
          if (b.count !== a.count) return b.count - a.count;
          return a.name.localeCompare(b.name);
        });

        setCategories(categoriesWithCounts);
      } else {
        // Use RPC data
        setCategories(categoriesData || []);
      }
    } catch (err: any) {
      console.error("Error fetching categories:", err);
      setError(err.message || "Failed to fetch categories");
      setCategories([]);
    } finally {
      setLoading(false);
    }
  };

  const createCategory = async (newCategory: NewCategory) => {
    try {
      // Use RPC function to add category
      const { data: rpcData, error: rpcError } = await (supabase as any).rpc(
        "add_category",
        {
          category_name: newCategory.name,
        }
      );

      if (rpcError) {
        console.error("RPC Error:", rpcError);
        return {
          data: null,
          error: rpcError.message || "Không thể thêm danh mục",
        };
      }

      // Refresh categories list
      await fetchCategories();

      return { data: rpcData, error: null };
    } catch (err: any) {
      console.error("Error creating category:", err);
      return { data: null, error: err.message || "Failed to create category" };
    }
  };

  const deleteCategory = async (categoryId: string, categoryName: string) => {
    try {
      // Use RPC function to delete category
      const { data: rpcData, error: rpcError } = await (supabase as any).rpc(
        "delete_category",
        {
          category_name: categoryName,
        }
      );

      if (rpcError) {
        console.error("RPC Error:", rpcError);
        return {
          data: null,
          error: rpcError.message || "Không thể xóa danh mục",
        };
      }

      // Refresh categories list
      await fetchCategories();

      return { data: rpcData, error: null };
    } catch (err: any) {
      console.error("Error deleting category:", err);
      return { data: null, error: err.message || "Failed to delete category" };
    }
  };

  const updateCategory = async (
    categoryId: string,
    updates: Partial<NewCategory>
  ) => {
    try {
      const updateData: any = { ...updates };

      // Generate new slug if name is being updated
      if (updates.name) {
        updateData.slug = updates.name
          .toLowerCase()
          .replace(/[àáạảãâầấậẩẫăằắặẳẵ]/g, "a")
          .replace(/[èéẹẻẽêềếệểễ]/g, "e")
          .replace(/[ìíịỉĩ]/g, "i")
          .replace(/[òóọỏõôồốộổỗơờớợởỡ]/g, "o")
          .replace(/[ùúụủũưừứựửữ]/g, "u")
          .replace(/[ỳýỵỷỹ]/g, "y")
          .replace(/đ/g, "d")
          .replace(/[^a-z0-9]/g, "-")
          .replace(/-+/g, "-")
          .replace(/^-|-$/g, "");
      }

      const { data, error: updateError } = await supabase
        .from("categories")
        .update(updateData)
        .eq("id", categoryId)
        .select()
        .single();

      if (updateError) throw updateError;

      await fetchCategories(); // Refresh the list
      return { data, error: null };
    } catch (err: any) {
      console.error("Error updating category:", err);
      return { data: null, error: err.message || "Failed to update category" };
    }
  };

  useEffect(() => {
    fetchCategories();
  }, []);

  return {
    categories,
    loading,
    error,
    fetchCategories,
    createCategory,
    deleteCategory,
    updateCategory,
    refetch: fetchCategories,
  };
};
