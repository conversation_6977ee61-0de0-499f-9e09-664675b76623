import { User } from "lucide-react";

// Users will be managed through authentication system
// For now, showing placeholder message

const Users = () => {
  // User management will be implemented with authentication system
  return (
    <div className="space-y-6">
      <div className="text-center py-12">
        <User className="w-16 h-16 mx-auto text-muted-foreground mb-4" />
        <h2 className="text-2xl font-bold text-foreground mb-2">Quản lý người dùng</h2>
        <p className="text-muted-foreground mb-4">
          Tính năng quản lý người dùng sẽ được tích hợp với hệ thống xác thực Supabase Auth
        </p>
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 max-w-md mx-auto">
          <p className="text-sm text-blue-800">
            <strong>Sắp ra mắt:</strong> Quản lý người dùng, ph<PERSON> quyền và xác thực
          </p>
        </div>
      </div>
    </div>
  );
};

export default Users;
