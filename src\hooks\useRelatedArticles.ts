import { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { format } from 'date-fns';
import { vi } from 'date-fns/locale';

export interface RelatedArticle {
  id: string;
  title: string;
  excerpt: string;
  date: string;
  category: string;
  readTime: string;
  slug: string;
}

export const useRelatedArticles = (currentArticleId?: string, currentCategory?: string) => {
  const [relatedArticles, setRelatedArticles] = useState<RelatedArticle[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const formatDate = (dateString: string) => {
    try {
      return format(new Date(dateString), 'dd/MM/yyyy', { locale: vi });
    } catch {
      return dateString;
    }
  };

  const calculateReadTime = (content: string) => {
    const wordsPerMinute = 200;
    const words = content.split(' ').length;
    const minutes = Math.ceil(words / wordsPerMinute);
    return `${minutes} phút`;
  };

  const fetchRelatedArticles = async () => {
    if (!currentArticleId) {
      setRelatedArticles([]);
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      setError(null);

      // Get all published articles except current one
      const { data: allArticles, error: fetchError } = await supabase
        .from('news_articles')
        .select('*')
        .eq('is_published', true)
        .neq('id', currentArticleId)
        .order('publish_date', { ascending: false });

      if (fetchError) throw fetchError;

      if (!allArticles || allArticles.length === 0) {
        setRelatedArticles([]);
        setLoading(false);
        return;
      }

      // Prioritize articles from same category
      const sameCategory = allArticles.filter(a => a.category === currentCategory);
      const otherCategory = allArticles.filter(a => a.category !== currentCategory);

      // Combine and limit to 3 articles
      const related = [...sameCategory, ...otherCategory].slice(0, 3);

      const formattedRelated = related.map(article => ({
        id: article.id,
        title: article.title,
        excerpt: article.excerpt,
        date: formatDate(article.publish_date),
        category: article.category,
        readTime: calculateReadTime(article.content || ''),
        slug: article.slug
      }));

      setRelatedArticles(formattedRelated);
    } catch (err: any) {
      console.error('Error fetching related articles:', err);
      setError(err.message || 'Failed to fetch related articles');
      
      // Fallback to mock data on error
      setRelatedArticles([
        {
          id: '2',
          title: "Ưu đãi cuối năm - Giảm 30% tất cả dịch vụ taxi đường dài",
          excerpt: "Nhân dịp cuối năm 2024, Vân Đăm Tour dành tặng khách hàng chương trình ưu đãi đặc biệt...",
          date: "10/12/2024",
          category: "Khuyến mãi",
          readTime: "2 phút",
          slug: "uu-dai-cuoi-nam"
        },
        {
          id: '3',
          title: "Hướng dẫn đặt xe online qua ứng dụng di động mới",
          excerpt: "Cách đặt xe taxi nhanh chóng và tiện lợi thông qua ứng dụng di động mới...",
          date: "05/12/2024",
          category: "Hướng dẫn",
          readTime: "4 phút",
          slug: "huong-dan-dat-xe-online"
        },
        {
          id: '5',
          title: "Mở rộng dịch vụ taxi sân bay Tân Sơn Nhất 24/7",
          excerpt: "Để đáp ứng nhu cầu ngày càng tăng của khách hàng, Vân Đăm Tour chính thức mở rộng...",
          date: "28/11/2024",
          category: "Tin mới",
          readTime: "3 phút",
          slug: "mo-rong-dich-vu-taxi-san-bay"
        }
      ]);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchRelatedArticles();
  }, [currentArticleId, currentCategory]);

  return {
    relatedArticles,
    loading,
    error,
    refetch: fetchRelatedArticles
  };
};
