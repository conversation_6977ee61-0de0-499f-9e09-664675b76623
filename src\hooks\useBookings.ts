import { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';

export interface Booking {
  id: string;
  booking_code: string;
  customer_name: string;
  customer_phone: string;
  customer_email?: string;
  pickup_location: string;
  destination: string;
  pickup_date: string;
  pickup_time: string;
  vehicle_type?: string;
  passenger_count: number;
  notes?: string;
  status: 'pending' | 'confirmed' | 'in_progress' | 'completed' | 'cancelled';
  estimated_price?: number;
  created_at: string;
  updated_at: string;
}

export interface BookingFilters {
  status?: string;
  date_from?: string;
  date_to?: string;
  customer_phone?: string;
  limit?: number;
}

export const useBookings = (filters: BookingFilters = {}) => {
  const [bookings, setBookings] = useState<Booking[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchBookings = async (customFilters?: BookingFilters) => {
    try {
      setLoading(true);
      setError(null);
      
      const activeFilters = { ...filters, ...customFilters };
      
      let query = supabase
        .from('bookings')
        .select('*');

      // Apply filters
      if (activeFilters.status) {
        query = query.eq('status', activeFilters.status);
      }

      if (activeFilters.date_from) {
        query = query.gte('pickup_date', activeFilters.date_from);
      }

      if (activeFilters.date_to) {
        query = query.lte('pickup_date', activeFilters.date_to);
      }

      if (activeFilters.customer_phone) {
        query = query.eq('customer_phone', activeFilters.customer_phone);
      }

      // Order by created_at descending
      query = query.order('created_at', { ascending: false });

      // Apply limit if specified
      if (activeFilters.limit) {
        query = query.limit(activeFilters.limit);
      }

      const { data, error: fetchError } = await query;
      
      if (fetchError) throw fetchError;
      
      setBookings(data || []);
    } catch (err: any) {
      console.error('Error fetching bookings:', err);
      setError(err.message || 'Failed to fetch bookings');
    } finally {
      setLoading(false);
    }
  };

  const createBooking = async (bookingData: Omit<Booking, 'id' | 'booking_code' | 'created_at' | 'updated_at'>) => {
    try {
      // Generate booking code
      const { data: bookingCode, error: codeError } = await supabase
        .rpc('generate_booking_code');
      
      if (codeError) throw codeError;

      const { data, error: createError } = await supabase
        .from('bookings')
        .insert([{
          ...bookingData,
          booking_code: bookingCode,
          status: 'pending'
        }])
        .select()
        .single();
      
      if (createError) throw createError;
      
      await fetchBookings(); // Refresh the list
      return { data, error: null };
    } catch (err: any) {
      console.error('Error creating booking:', err);
      return { data: null, error: err.message || 'Failed to create booking' };
    }
  };

  const updateBooking = async (id: string, updates: Partial<Booking>) => {
    try {
      const { error: updateError } = await supabase
        .from('bookings')
        .update({
          ...updates,
          updated_at: new Date().toISOString()
        })
        .eq('id', id);
      
      if (updateError) throw updateError;
      
      await fetchBookings(); // Refresh the list
      return { error: null };
    } catch (err: any) {
      console.error('Error updating booking:', err);
      return { error: err.message || 'Failed to update booking' };
    }
  };

  const updateBookingStatus = async (id: string, status: Booking['status']) => {
    return updateBooking(id, { status });
  };

  const deleteBooking = async (id: string) => {
    try {
      const { error: deleteError } = await supabase
        .from('bookings')
        .delete()
        .eq('id', id);
      
      if (deleteError) throw deleteError;
      
      await fetchBookings(); // Refresh the list
      return { error: null };
    } catch (err: any) {
      console.error('Error deleting booking:', err);
      return { error: err.message || 'Failed to delete booking' };
    }
  };

  useEffect(() => {
    fetchBookings();
  }, []);

  return {
    bookings,
    loading,
    error,
    fetchBookings,
    createBooking,
    updateBooking,
    updateBookingStatus,
    deleteBooking,
    refetch: fetchBookings
  };
};

// Hook for getting a single booking by booking_code
export const useBooking = (bookingCode: string) => {
  const [booking, setBooking] = useState<Booking | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchBooking = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const { data, error: fetchError } = await supabase
        .from('bookings')
        .select('*')
        .eq('booking_code', bookingCode)
        .single();
      
      if (fetchError) {
        if (fetchError.code === 'PGRST116') {
          setBooking(null);
        } else {
          throw fetchError;
        }
      } else {
        setBooking(data);
      }
    } catch (err: any) {
      console.error(`Error fetching booking ${bookingCode}:`, err);
      setError(err.message || 'Failed to fetch booking');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (bookingCode) {
      fetchBooking();
    }
  }, [bookingCode]);

  return {
    booking,
    loading,
    error,
    refetch: fetchBooking
  };
};

// Hook for booking statistics
export const useBookingStats = () => {
  const [stats, setStats] = useState({
    total: 0,
    pending: 0,
    confirmed: 0,
    completed: 0,
    cancelled: 0
  });
  const [loading, setLoading] = useState(true);

  const fetchStats = async () => {
    try {
      setLoading(true);
      
      const { data, error } = await supabase
        .from('bookings')
        .select('status');
      
      if (error) throw error;
      
      const newStats = {
        total: data?.length || 0,
        pending: data?.filter(b => b.status === 'pending').length || 0,
        confirmed: data?.filter(b => b.status === 'confirmed').length || 0,
        completed: data?.filter(b => b.status === 'completed').length || 0,
        cancelled: data?.filter(b => b.status === 'cancelled').length || 0
      };
      
      setStats(newStats);
    } catch (err: any) {
      console.error('Error fetching booking stats:', err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchStats();
  }, []);

  return {
    stats,
    loading,
    refetch: fetchStats
  };
};
