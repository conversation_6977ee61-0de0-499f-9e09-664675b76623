import React from 'react';

interface ZaloIconProps {
  className?: string;
}

export const ZaloIcon: React.FC<ZaloIconProps> = ({ className = "w-4 h-4" }) => {
  return (
    <svg
      viewBox="0 0 24 24"
      className={className}
      fill="currentColor"
      xmlns="http://www.w3.org/2000/svg"
    >
      {/* Zalo logo - simplified version */}
      <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8z" />
      <path d="M8 10h8v1H8zm0 2h8v1H8zm0 2h6v1H8z" />
      <circle cx="16" cy="14" r="1" />
    </svg>
  );
};
