import { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';

export interface Service {
  id: string;
  service_id: string;
  title: string;
  description: string;
  icon_url: string;
  features: string[];
  is_active: boolean;
  display_order: number;
  created_at: string;
  updated_at: string;
}

export const useServices = () => {
  const [services, setServices] = useState<Service[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchServices = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const { data, error: fetchError } = await supabase
        .from('services')
        .select('*')
        .eq('is_active', true)
        .order('display_order', { ascending: true });
      
      if (fetchError) throw fetchError;
      
      setServices(data || []);
    } catch (err: any) {
      console.error('Error fetching services:', err);
      setError(err.message || 'Failed to fetch services');
    } finally {
      setLoading(false);
    }
  };

  const createService = async (serviceData: Omit<Service, 'id' | 'created_at' | 'updated_at'>) => {
    try {
      const { data, error: createError } = await supabase
        .from('services')
        .insert([serviceData])
        .select()
        .single();
      
      if (createError) throw createError;
      
      await fetchServices(); // Refresh the list
      return { data, error: null };
    } catch (err: any) {
      console.error('Error creating service:', err);
      return { data: null, error: err.message || 'Failed to create service' };
    }
  };

  const updateService = async (id: string, updates: Partial<Service>) => {
    try {
      const { error: updateError } = await supabase
        .from('services')
        .update({
          ...updates,
          updated_at: new Date().toISOString()
        })
        .eq('id', id);
      
      if (updateError) throw updateError;
      
      await fetchServices(); // Refresh the list
      return { error: null };
    } catch (err: any) {
      console.error('Error updating service:', err);
      return { error: err.message || 'Failed to update service' };
    }
  };

  const deleteService = async (id: string) => {
    try {
      // Soft delete by setting is_active to false
      const { error: deleteError } = await supabase
        .from('services')
        .update({ 
          is_active: false,
          updated_at: new Date().toISOString()
        })
        .eq('id', id);
      
      if (deleteError) throw deleteError;
      
      await fetchServices(); // Refresh the list
      return { error: null };
    } catch (err: any) {
      console.error('Error deleting service:', err);
      return { error: err.message || 'Failed to delete service' };
    }
  };

  const reorderServices = async (serviceIds: string[]) => {
    try {
      // Update display_order for each service
      const updates = serviceIds.map((id, index) => 
        supabase
          .from('services')
          .update({ 
            display_order: index + 1,
            updated_at: new Date().toISOString()
          })
          .eq('id', id)
      );
      
      await Promise.all(updates);
      await fetchServices(); // Refresh the list
      return { error: null };
    } catch (err: any) {
      console.error('Error reordering services:', err);
      return { error: err.message || 'Failed to reorder services' };
    }
  };

  useEffect(() => {
    fetchServices();
  }, []);

  return {
    services,
    loading,
    error,
    fetchServices,
    createService,
    updateService,
    deleteService,
    reorderServices,
    refetch: fetchServices
  };
};

// Hook for getting a single service by service_id
export const useService = (serviceId: string) => {
  const [service, setService] = useState<Service | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchService = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const { data, error: fetchError } = await supabase
        .from('services')
        .select('*')
        .eq('service_id', serviceId)
        .eq('is_active', true)
        .single();
      
      if (fetchError) {
        if (fetchError.code === 'PGRST116') {
          setService(null);
        } else {
          throw fetchError;
        }
      } else {
        setService(data);
      }
    } catch (err: any) {
      console.error(`Error fetching service ${serviceId}:`, err);
      setError(err.message || 'Failed to fetch service');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (serviceId) {
      fetchService();
    }
  }, [serviceId]);

  return {
    service,
    loading,
    error,
    refetch: fetchService
  };
};
