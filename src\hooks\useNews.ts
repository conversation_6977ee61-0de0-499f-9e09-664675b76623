import { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';

export interface NewsArticle {
  id: string;
  title: string;
  slug: string;
  excerpt: string;
  content: string;
  author: string;
  category: string;
  featured_image: string;
  is_featured: boolean;
  is_published: boolean;
  views: number;
  publish_date: string;
  created_at: string;
  updated_at: string;
}

export interface NewsFilters {
  category?: string;
  featured?: boolean;
  published?: boolean;
  limit?: number;
}

export const useNews = (filters: NewsFilters = {}) => {
  const [articles, setArticles] = useState<NewsArticle[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchNews = async (customFilters?: NewsFilters) => {
    try {
      setLoading(true);
      setError(null);
      
      const activeFilters = { ...filters, ...customFilters };
      
      let query = supabase
        .from('news_articles')
        .select('*');

      // Apply filters
      if (activeFilters.published !== undefined) {
        query = query.eq('is_published', activeFilters.published);
      } else {
        // Default to published articles for public view
        query = query.eq('is_published', true);
      }

      if (activeFilters.category) {
        query = query.eq('category', activeFilters.category);
      }

      if (activeFilters.featured !== undefined) {
        query = query.eq('is_featured', activeFilters.featured);
      }

      // Order by publish_date descending
      query = query.order('publish_date', { ascending: false });

      // Apply limit if specified
      if (activeFilters.limit) {
        query = query.limit(activeFilters.limit);
      }

      const { data, error: fetchError } = await query;
      
      if (fetchError) throw fetchError;
      
      setArticles(data || []);
    } catch (err: any) {
      console.error('Error fetching news:', err);
      setError(err.message || 'Failed to fetch news');
    } finally {
      setLoading(false);
    }
  };

  const createArticle = async (articleData: Omit<NewsArticle, 'id' | 'created_at' | 'updated_at' | 'views'>) => {
    try {
      const { data, error: createError } = await supabase
        .from('news_articles')
        .insert([{
          ...articleData,
          slug: generateSlug(articleData.title),
          views: 0,
          publish_date: articleData.is_published ? new Date().toISOString() : null
        }])
        .select()
        .single();
      
      if (createError) throw createError;
      
      await fetchNews(); // Refresh the list
      return { data, error: null };
    } catch (err: any) {
      console.error('Error creating article:', err);
      return { data: null, error: err.message || 'Failed to create article' };
    }
  };

  const updateArticle = async (id: string, updates: Partial<NewsArticle>) => {
    try {
      const updateData: any = {
        ...updates,
        updated_at: new Date().toISOString()
      };

      // Update slug if title changed
      if (updates.title) {
        updateData.slug = generateSlug(updates.title);
      }

      // Set publish_date if publishing for the first time
      if (updates.is_published && !updates.publish_date) {
        updateData.publish_date = new Date().toISOString();
      }

      const { error: updateError } = await supabase
        .from('news_articles')
        .update(updateData)
        .eq('id', id);
      
      if (updateError) throw updateError;
      
      await fetchNews(); // Refresh the list
      return { error: null };
    } catch (err: any) {
      console.error('Error updating article:', err);
      return { error: err.message || 'Failed to update article' };
    }
  };

  const deleteArticle = async (id: string) => {
    try {
      const { error: deleteError } = await supabase
        .from('news_articles')
        .delete()
        .eq('id', id);
      
      if (deleteError) throw deleteError;
      
      await fetchNews(); // Refresh the list
      return { error: null };
    } catch (err: any) {
      console.error('Error deleting article:', err);
      return { error: err.message || 'Failed to delete article' };
    }
  };

  const incrementViews = async (id: string) => {
    try {
      await supabase.rpc('increment_news_views', { article_id: id });
    } catch (err: any) {
      console.error('Error incrementing views:', err);
    }
  };

  useEffect(() => {
    fetchNews();
  }, []);

  return {
    articles,
    loading,
    error,
    fetchNews,
    createArticle,
    updateArticle,
    deleteArticle,
    incrementViews,
    refetch: fetchNews
  };
};

// Hook for getting a single article by slug
export const useNewsArticle = (slug: string) => {
  const [article, setArticle] = useState<NewsArticle | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchArticle = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const { data, error: fetchError } = await supabase
        .from('news_articles')
        .select('*')
        .eq('slug', slug)
        .eq('is_published', true)
        .single();
      
      if (fetchError) {
        if (fetchError.code === 'PGRST116') {
          setArticle(null);
        } else {
          throw fetchError;
        }
      } else {
        setArticle(data);
        // Increment views when article is fetched
        if (data?.id) {
          await supabase.rpc('increment_news_views', { article_id: data.id });
        }
      }
    } catch (err: any) {
      console.error(`Error fetching article ${slug}:`, err);
      setError(err.message || 'Failed to fetch article');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (slug) {
      fetchArticle();
    }
  }, [slug]);

  return {
    article,
    loading,
    error,
    refetch: fetchArticle
  };
};

// Utility function to generate slug from title
function generateSlug(title: string): string {
  return title
    .toLowerCase()
    .normalize('NFD')
    .replace(/[\u0300-\u036f]/g, '') // Remove diacritics
    .replace(/[đĐ]/g, 'd') // Replace Vietnamese đ
    .replace(/[^a-z0-9\s-]/g, '') // Remove special characters
    .replace(/\s+/g, '-') // Replace spaces with hyphens
    .replace(/-+/g, '-') // Replace multiple hyphens with single
    .trim()
    .replace(/^-+|-+$/g, ''); // Remove leading/trailing hyphens
}

// Hook for getting news categories
export const useNewsCategories = () => {
  const [categories, setCategories] = useState<string[]>([]);
  const [loading, setLoading] = useState(true);

  const fetchCategories = async () => {
    try {
      setLoading(true);
      
      const { data, error } = await supabase
        .from('news_articles')
        .select('category')
        .eq('is_published', true);
      
      if (error) throw error;
      
      // Get unique categories
      const uniqueCategories = [...new Set(data?.map(item => item.category).filter(Boolean))];
      setCategories(uniqueCategories);
    } catch (err: any) {
      console.error('Error fetching categories:', err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchCategories();
  }, []);

  return {
    categories,
    loading,
    refetch: fetchCategories
  };
};
