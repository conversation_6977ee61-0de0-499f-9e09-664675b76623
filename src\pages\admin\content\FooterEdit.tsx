import { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Separator } from "@/components/ui/separator";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { ArrowLeft, Save, RotateCcw, Plus, Trash2, Eye, Facebook, MessageCircle, Phone, Instagram } from "lucide-react";
import { useNavigate } from "react-router-dom";
import { toast } from "sonner";
import PreviewModal from "@/components/admin/preview/PreviewModal";
import FooterPreview from "@/components/admin/preview/FooterPreview";
import { useSupabaseContent } from "@/hooks/useSupabaseContent";

const FooterEdit = () => {
  const navigate = useNavigate();
  const [showPreview, setShowPreview] = useState(false);

  // Get current footer config from Supabase
  const { data: footerData, updateContent, createContent } = useSupabaseContent('footer');

  // Available social media icons
  const socialIconOptions = [
    { value: "facebook", label: "Facebook", icon: Facebook },
    { value: "zalo", label: "Zalo", icon: MessageCircle },
    { value: "phone", label: "Phone", icon: Phone },
    { value: "instagram", label: "Instagram", icon: Instagram }
  ];

  // Footer configuration state with fallback
  const defaultFooterConfig = {
    company: {
      name: "BICOM",
      description: "Dịch vụ taxi uy tín và chuyên nghiệp tại TP. Hồ Chí Minh. Chúng tôi cam kết mang đến cho khách hàng những chuyến đi an toàn, thoải mái với giá cả hợp lý."
    },
    contact: {
      address: "TP. Hồ Chí Minh, Việt Nam",
      phone: "0823141862",
      email: "<EMAIL>",
      website: "www.bicom.com"
    },
    services: [
      "Taxi trong thành phố",
      "Taxi đường dài",
      "Taxi hợp đồng",
      "Taxi sân bay"
    ],
    socialLinks: [
      { name: "Facebook", url: "https://facebook.com/bicom", icon: "facebook" },
      { name: "Zalo", url: "https://zalo.me/0823141862", icon: "zalo" },
      { name: "Phone", url: "tel:0823141862", icon: "phone" }
    ],
    copyright: "© 2024 BICOM. Tất cả quyền được bảo lưu.",
    additionalInfo: "CÔNG TY TNHH SẢN XUẤT VÀ THƯƠNG MẠI DỊCH VỤ BICOM - Mã số thuế: 0315360616"
  };

  const [footerConfig, setFooterConfig] = useState(defaultFooterConfig);

  // Update state when data loads from database
  useEffect(() => {
    if (footerData?.content) {
      // Merge database data with default config to ensure all fields have values
      setFooterConfig(prevConfig => ({
        ...defaultFooterConfig,
        ...footerData.content,
        // Ensure nested objects are properly merged
        company: {
          ...defaultFooterConfig.company,
          ...(footerData.content.company || {})
        },
        contact: {
          ...defaultFooterConfig.contact,
          ...(footerData.content.contact || {})
        },
        socialLinks: footerData.content.socialLinks || defaultFooterConfig.socialLinks,
        services: footerData.content.services || defaultFooterConfig.services
      }));
    }
  }, [footerData]);

  const handleSave = async () => {
    try {
      if (footerData) {
        // Update existing record
        const { error } = await updateContent({ content: footerConfig });
        if (error) {
          toast.error(`Lỗi: ${error}`);
          return;
        }
      } else {
        // Create new record
        const { error } = await createContent({
          section_key: 'footer',
          title: 'Footer Configuration',
          description: 'Website footer content and settings',
          content: footerConfig,
          is_active: true
        });
        if (error) {
          toast.error(`Lỗi: ${error}`);
          return;
        }
      }
      toast.success("Đã lưu cấu hình footer thành công!");
    } catch (error) {
      toast.error("Có lỗi xảy ra khi lưu cấu hình footer");
    }
  };

  const handleReset = () => {
    // Reset to current Supabase data with proper merging
    if (footerData?.content) {
      setFooterConfig({
        ...defaultFooterConfig,
        ...footerData.content,
        company: {
          ...defaultFooterConfig.company,
          ...(footerData.content.company || {})
        },
        contact: {
          ...defaultFooterConfig.contact,
          ...(footerData.content.contact || {})
        },
        socialLinks: footerData.content.socialLinks || defaultFooterConfig.socialLinks,
        services: footerData.content.services || defaultFooterConfig.services
      });
    } else {
      setFooterConfig(defaultFooterConfig);
    }
    toast.info("Đã khôi phục về cài đặt hiện tại");
  };

  const addService = () => {
    setFooterConfig({
      ...footerConfig,
      services: [...footerConfig.services, "Dịch vụ mới"]
    });
  };

  const updateService = (index: number, value: string) => {
    const newServices = [...footerConfig.services];
    newServices[index] = value;
    setFooterConfig({
      ...footerConfig,
      services: newServices
    });
  };

  const removeService = (index: number) => {
    const newServices = footerConfig.services.filter((_, i) => i !== index);
    setFooterConfig({
      ...footerConfig,
      services: newServices
    });
  };

  const addSocialLink = () => {
    setFooterConfig({
      ...footerConfig,
      socialLinks: [...footerConfig.socialLinks, { name: "Mạng xã hội mới", url: "", icon: "link" }]
    });
  };

  const updateSocialLink = (index: number, field: string, value: string) => {
    const newLinks = [...footerConfig.socialLinks];
    newLinks[index] = { ...newLinks[index], [field]: value };
    setFooterConfig({
      ...footerConfig,
      socialLinks: newLinks
    });
  };

  const removeSocialLink = (index: number) => {
    const newLinks = footerConfig.socialLinks.filter((_, i) => i !== index);
    setFooterConfig({
      ...footerConfig,
      socialLinks: newLinks
    });
  };

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button
            variant="ghost"
            size="icon"
            onClick={() => navigate("/admin/content")}
          >
            <ArrowLeft className="w-4 h-4" />
          </Button>
          <div>
            <h1 className="text-2xl font-bold">Chỉnh sửa Footer</h1>
            <p className="text-muted-foreground">Quản lý thông tin liên hệ, bản quyền và liên kết mạng xã hội</p>
          </div>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" onClick={() => setShowPreview(true)}>
            <Eye className="w-4 h-4 mr-2" />
            Xem trước
          </Button>
          <Button variant="outline" onClick={handleReset}>
            <RotateCcw className="w-4 h-4 mr-2" />
            Khôi phục
          </Button>
          <Button onClick={handleSave}>
            <Save className="w-4 h-4 mr-2" />
            Lưu thay đổi
          </Button>
        </div>
      </div>

      <div className="grid gap-6">
        {/* Company Information */}
        <Card>
          <CardHeader>
            <CardTitle>Thông tin công ty</CardTitle>
            <CardDescription>
              Tên công ty và mô tả hiển thị trong footer
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid gap-2">
              <Label htmlFor="company-name">Tên công ty</Label>
              <Input
                id="company-name"
                value={footerConfig.company.name}
                onChange={(e) => setFooterConfig({
                  ...footerConfig,
                  company: { ...footerConfig.company, name: e.target.value }
                })}
                placeholder="Vân Đăm Tour"
              />
            </div>
            <div className="grid gap-2">
              <Label htmlFor="company-desc">Mô tả công ty</Label>
              <Textarea
                id="company-desc"
                value={footerConfig.company.description}
                onChange={(e) => setFooterConfig({
                  ...footerConfig,
                  company: { ...footerConfig.company, description: e.target.value }
                })}
                rows={3}
                placeholder="Mô tả về công ty và dịch vụ..."
              />
            </div>
          </CardContent>
        </Card>

        {/* Contact Information */}
        <Card>
          <CardHeader>
            <CardTitle>Thông tin liên hệ</CardTitle>
            <CardDescription>
              Địa chỉ, số điện thoại, email và website
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid gap-2">
              <Label htmlFor="address">Địa chỉ</Label>
              <Input
                id="address"
                value={footerConfig.contact.address}
                onChange={(e) => setFooterConfig({
                  ...footerConfig,
                  contact: { ...footerConfig.contact, address: e.target.value }
                })}
                placeholder="Đà Lạt, Lâm Đồng"
              />
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div className="grid gap-2">
                <Label htmlFor="phone">Số điện thoại</Label>
                <Input
                  id="phone"
                  value={footerConfig.contact.phone}
                  onChange={(e) => setFooterConfig({
                    ...footerConfig,
                    contact: { ...footerConfig.contact, phone: e.target.value }
                  })}
                  placeholder="0823141862"
                />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="email">Email</Label>
                <Input
                  id="email"
                  value={footerConfig.contact.email}
                  onChange={(e) => setFooterConfig({
                    ...footerConfig,
                    contact: { ...footerConfig.contact, email: e.target.value }
                  })}
                  placeholder="<EMAIL>"
                />
              </div>
            </div>
            <div className="grid gap-2">
              <Label htmlFor="website">Website</Label>
              <Input
                id="website"
                value={footerConfig.contact.website}
                onChange={(e) => setFooterConfig({
                  ...footerConfig,
                  contact: { ...footerConfig.contact, website: e.target.value }
                })}
                placeholder="www.vandamtour.com"
              />
            </div>
          </CardContent>
        </Card>

        {/* Services List */}
        <Card>
          <CardHeader>
            <CardTitle>Danh sách dịch vụ</CardTitle>
            <CardDescription>
              Các dịch vụ hiển thị trong footer
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {footerConfig.services.map((service, index) => (
              <div key={index} className="flex gap-2">
                <Input
                  value={service}
                  onChange={(e) => updateService(index, e.target.value)}
                  placeholder="Tên dịch vụ"
                  className="flex-1"
                />
                <Button
                  variant="outline"
                  size="icon"
                  onClick={() => removeService(index)}
                  disabled={footerConfig.services.length <= 1}
                >
                  <Trash2 className="w-4 h-4" />
                </Button>
              </div>
            ))}
            <Button variant="outline" onClick={addService}>
              <Plus className="w-4 h-4 mr-2" />
              Thêm dịch vụ
            </Button>
          </CardContent>
        </Card>

        {/* Social Links */}
        <Card>
          <CardHeader>
            <CardTitle>Liên kết mạng xã hội</CardTitle>
            <CardDescription>
              Các liên kết đến mạng xã hội và kênh liên hệ
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {footerConfig.socialLinks.map((link, index) => (
              <div key={index} className="flex gap-2">
                <Input
                  value={link.name}
                  onChange={(e) => updateSocialLink(index, "name", e.target.value)}
                  placeholder="Tên mạng xã hội"
                  className="flex-1"
                />
                <Input
                  value={link.url}
                  onChange={(e) => updateSocialLink(index, "url", e.target.value)}
                  placeholder="https://..."
                  className="flex-1"
                />
                <Select
                  value={link.icon}
                  onValueChange={(value) => updateSocialLink(index, "icon", value)}
                >
                  <SelectTrigger className="w-32">
                    <SelectValue placeholder="Chọn icon" />
                  </SelectTrigger>
                  <SelectContent>
                    {socialIconOptions.map((option) => {
                      const IconComponent = option.icon;
                      return (
                        <SelectItem key={option.value} value={option.value}>
                          <div className="flex items-center gap-2">
                            <IconComponent className="w-4 h-4" />
                            {option.label}
                          </div>
                        </SelectItem>
                      );
                    })}
                  </SelectContent>
                </Select>
                <Button
                  variant="outline"
                  size="icon"
                  onClick={() => removeSocialLink(index)}
                >
                  <Trash2 className="w-4 h-4" />
                </Button>
              </div>
            ))}
            <Button variant="outline" onClick={addSocialLink}>
              <Plus className="w-4 h-4 mr-2" />
              Thêm liên kết
            </Button>
          </CardContent>
        </Card>

        {/* Copyright & Additional Info */}
        <Card>
          <CardHeader>
            <CardTitle>Bản quyền & Thông tin bổ sung</CardTitle>
            <CardDescription>
              Thông tin bản quyền và các thông tin pháp lý khác
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid gap-2">
              <Label htmlFor="copyright">Thông tin bản quyền</Label>
              <Input
                id="copyright"
                value={footerConfig.copyright}
                onChange={(e) => setFooterConfig({
                  ...footerConfig,
                  copyright: e.target.value
                })}
                placeholder="© 2024 Vân Đăm Tour. Tất cả quyền được bảo lưu."
              />
            </div>
            <div className="grid gap-2">
              <Label htmlFor="additional-info">Thông tin bổ sung</Label>
              <Textarea
                id="additional-info"
                value={footerConfig.additionalInfo}
                onChange={(e) => setFooterConfig({
                  ...footerConfig,
                  additionalInfo: e.target.value
                })}
                rows={2}
                placeholder="Giấy phép kinh doanh, thông tin pháp lý..."
              />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Preview Modal */}
      <PreviewModal
        isOpen={showPreview}
        onClose={() => setShowPreview(false)}
        title="Footer"
      >
        <FooterPreview config={footerConfig} />
      </PreviewModal>
    </div>
  );
};

export default FooterEdit;