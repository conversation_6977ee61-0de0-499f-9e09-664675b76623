# Bicom Now - Taxi Service Platform

A modern taxi booking and management platform built with React, TypeScript, and Supabase.

## Features

- **Taxi Booking System**: Easy-to-use booking form with vehicle selection
- **Admin Dashboard**: Complete content management system
- **Real-time Data**: Powered by Supabase for real-time updates
- **Responsive Design**: Mobile-first design with Tailwind CSS
- **Modern UI**: Built with shadcn/ui components

## Tech Stack

- **Frontend**: React 18, TypeScript, Vite
- **Styling**: Tailwind CSS, shadcn/ui
- **Backend**: Supabase (Database, Auth, Real-time)
- **State Management**: Redux Toolkit
- **Forms**: React Hook Form with Zod validation
- **Icons**: Lucide React

## Getting Started

### Prerequisites

- Node.js 18+
- npm or yarn

### Installation

1. Clone the repository

```bash
git clone <repository-url>
cd bicom-now
```

2. Install dependencies

```bash
npm install
```

3. Set up environment variables

```bash
cp .env.example .env.local
```

4. Start the development server

```bash
npm run dev
```

## Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run preview` - Preview production build
- `npm run lint` - Run ESLint

## Project Structure

```
src/
├── components/          # Reusable UI components
├── pages/              # Page components
├── hooks/              # Custom React hooks
├── store/              # Redux store configuration
├── lib/                # Utility functions
└── integrations/       # External service integrations
```

## License

Private project - All rights reserved.
