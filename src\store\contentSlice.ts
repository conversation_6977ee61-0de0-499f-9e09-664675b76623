import { createSlice, PayloadAction } from "@reduxjs/toolkit";

export interface HeaderConfig {
  logo: {
    text: string;
    description: string;
  };
  navigation: {
    items: Array<{ label: string; href: string }>;
  };
  contact: {
    phone: string;
    showPhone: boolean;
    address: string;
    showAddress: boolean;
  };
}

export interface ServiceItem {
  id: string;
  title: string;
  description: string;
  icon: string;
  features: string[] | string;
}

export interface ServicesConfig {
  sectionTitle: string;
  sectionDescription: string;
  services: ServiceItem[];
}

export interface FloatingContactsConfig {
  enabled: boolean;
  phone: {
    enabled: boolean;
    number: string;
    showNumber: boolean;
    backgroundColor: string;
    hoverColor: string;
  };
  zalo: {
    enabled: boolean;
    number: string;
    backgroundColor: string;
    hoverColor: string;
    showNotificationDot: boolean;
  };
  scrollToTop: {
    enabled: boolean;
    backgroundColor: string;
    hoverColor: string;
  };
  position: {
    side: string;
    bottomOffset: number;
    sideOffset: number;
  };
}

// Simplified content state - most data now comes from Supabase
interface ContentState {
  // Keep only essential UI state that doesn't need to be in database
  isLoading: boolean;
  error: string | null;
}

const initialState: ContentState = {
  isLoading: false,
  error: null,
};

const contentSlice = createSlice({
  name: "content",
  initialState,
  reducers: {
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.isLoading = action.payload;
    },
    setError: (state, action: PayloadAction<string | null>) => {
      state.error = action.payload;
    },
  },
});

export const { setLoading, setError } = contentSlice.actions;
export default contentSlice.reducer;
