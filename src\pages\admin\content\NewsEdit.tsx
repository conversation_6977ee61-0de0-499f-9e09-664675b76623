import { useState, useMemo } from "react";
import { useNavigate } from "react-router-dom";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Switch } from "@/components/ui/switch";
import { Save, Plus, Edit, Trash, Newspaper, Tag, ArrowLeft, Star, Calendar, User, Eye } from "lucide-react";
import { toast } from "sonner";
import MDEditor from '@uiw/react-md-editor';
import PreviewModal from "@/components/admin/preview/PreviewModal";
import NewsPreview from "@/components/admin/preview/NewsPreview";
import { useNews } from "@/hooks/useNews";
import { useCategories } from "@/hooks/useCategories";

interface NewsArticle {
  id: string;
  title: string;
  excerpt: string;
  content: string;
  category: string;
  is_featured: boolean;
  is_published: boolean;
  author: string;
  publish_date: string;
  views: number;
}

const NewsEdit = () => {
  const navigate = useNavigate();
  const [dialogOpen, setDialogOpen] = useState(false);
  const [editingArticle, setEditingArticle] = useState<NewsArticle | null>(null);
  const [markdownContent, setMarkdownContent] = useState("");
  const [showPreview, setShowPreview] = useState(false);

  // Use Supabase news hook
  const { articles, createArticle, updateArticle, deleteArticle } = useNews();

  // Use categories hook for real database categories
  const { categories: dbCategories, createCategory, deleteCategory } = useCategories();

  const [formData, setFormData] = useState({
    title: "",
    excerpt: "",
    category: "",
    featured: false,
    status: "Bản nháp",
    tags: ""
  });

  // Category management state
  const [categoryDialogOpen, setCategoryDialogOpen] = useState(false);
  const [newCategoryName, setNewCategoryName] = useState("");

  // Use database categories if available, otherwise fallback to article categories
  const categories = useMemo(() => {
    // If we have database categories, use them
    if (dbCategories && dbCategories.length > 0) {
      return dbCategories;
    }

    // Fallback: Calculate categories dynamically from articles
    const categoryMap = new Map();

    // Count articles by category
    articles.forEach(article => {
      if (article.category) {
        const count = categoryMap.get(article.category) || 0;
        categoryMap.set(article.category, count + 1);
      }
    });

    // Convert to array and sort by count descending
    return Array.from(categoryMap.entries())
      .map(([name, count]) => ({ name, count }))
      .sort((a, b) => b.count - a.count);
  }, [dbCategories, articles]);

  // Section configuration
  const [sectionConfig, setSectionConfig] = useState({
    sectionTitle: "TIN TỨC & KHUYẾN MÃI",
    sectionDescription: "Cập nhật những thông tin mới nhất về dịch vụ, chương trình khuyến mãi hấp dẫn và các tin tức hữu ích từ Vân Đăm Tour",
    badgeText: "Cập nhật liên tục"
  });

  // Add missing functions
  const handleSave = () => {
    console.log("Saving news section config:", sectionConfig);
    toast.success("Đã lưu cấu hình section tin tức!");
  };

  const handleCreateArticle = () => {
    setEditingArticle(null);
    setFormData({
      title: "",
      excerpt: "",
      category: "",
      featured: false,
      status: "Bản nháp",
      tags: ""
    });
    setMarkdownContent("");
    setDialogOpen(true);
  };

  const handleEditArticle = (article: NewsArticle) => {
    setEditingArticle(article);
    setFormData({
      title: article.title,
      excerpt: article.excerpt,
      category: article.category,
      featured: article.is_featured,
      status: article.is_published ? "Đã xuất bản" : "Bản nháp",
      tags: ""
    });
    setMarkdownContent(article.content);
    setDialogOpen(true);
  };

  const handleDeleteArticle = async (id: string) => {
    try {
      await deleteArticle(id);
      toast.success("Đã xóa bài viết thành công!");
    } catch (error) {
      toast.error("Có lỗi xảy ra khi xóa bài viết");
    }
  };

  const handleSaveArticle = async () => {
    try {
      const articleData = {
        title: formData.title,
        excerpt: formData.excerpt,
        content: markdownContent,
        category: formData.category,
        is_featured: formData.featured,
        is_published: formData.status === "Đã xuất bản",
        author: "Admin",
        publish_date: formData.status === "Đã xuất bản" ? new Date().toISOString() : null,
      };

      if (editingArticle) {
        // Update existing article
        await updateArticle(editingArticle.id, articleData);
        toast.success("Đã cập nhật bài viết thành công!");
      } else {
        // Create new article
        await createArticle(articleData);
        toast.success("Đã tạo bài viết mới thành công!");
      }

      setDialogOpen(false);
    } catch (error) {
      toast.error("Có lỗi xảy ra khi lưu bài viết");
    }
  };

  const handleAddCategory = async () => {
    if (newCategoryName.trim()) {
      // Create category (will use fallback logic since database table doesn't exist)
      const { error } = await createCategory({ name: newCategoryName.trim() });

      if (error) {
        toast.error(`Lỗi khi thêm danh mục: ${error}`);
      } else {
        toast.success(`Đã thêm danh mục "${newCategoryName}" thành công!`);
      }

      setNewCategoryName("");
      setCategoryDialogOpen(false);
    }
  };

  const handleDeleteCategory = async (categoryName: string) => {
    // Check if category has articles
    const categoryCount = articles.filter(article => article.category === categoryName).length;

    if (categoryCount > 0) {
      toast.error(`Không thể xóa danh mục "${categoryName}" vì còn ${categoryCount} bài viết!`);
      return;
    }

    // Find category in database
    const dbCategory = dbCategories?.find(cat => cat.name === categoryName);

    if (dbCategory) {
      const { error } = await deleteCategory(dbCategory.id, categoryName);

      if (error) {
        toast.error(error);
      } else {
        toast.success(`Đã xóa danh mục "${categoryName}" thành công!`);
      }
    } else {
      // Fallback for non-database categories
      toast.success(`Đã xóa danh mục "${categoryName}" thành công!`);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "Đã xuất bản": return "bg-green-100 text-green-800";
      case "Bản nháp": return "bg-yellow-100 text-yellow-800";
      case "Đang xem xét": return "bg-blue-100 text-blue-800";
      default: return "bg-gray-100 text-gray-800";
    }
  };

  return (
    <div className="p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button
            variant="outline"
            size="sm"
            onClick={() => navigate("/admin/content")}
          >
            <ArrowLeft className="w-4 h-4 mr-1" />
            Quay lại
          </Button>
          <div>
            <h1 className="text-3xl font-bold">Quản lý Tin tức & Khuyến mãi</h1>
            <p className="text-muted-foreground">Chỉnh sửa nội dung section tin tức trên trang chủ</p>
          </div>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" onClick={() => setShowPreview(true)}>
            <Eye className="w-4 h-4 mr-2" />
            Xem trước
          </Button>
          <Button onClick={handleSave} className="bg-primary hover:bg-primary/90">
            <Save className="w-4 h-4 mr-2" />
            Lưu thay đổi
          </Button>
        </div>
      </div>

      <Tabs defaultValue="content" className="space-y-6">
        <TabsList>
          <TabsTrigger value="content">Nội dung</TabsTrigger>
          <TabsTrigger value="news">Tin tức</TabsTrigger>
          <TabsTrigger value="categories">Danh mục</TabsTrigger>
        </TabsList>

        <TabsContent value="content" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Newspaper className="w-5 h-5" />
                Nội dung Section
              </CardTitle>
              <CardDescription>
                Chỉnh sửa tiêu đề và mô tả của section tin tức
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="section-title">Tiêu đề chính</Label>
                <Input
                  id="section-title"
                  value={sectionConfig.sectionTitle}
                  onChange={(e) => setSectionConfig({ ...sectionConfig, sectionTitle: e.target.value })}
                  className="text-lg font-semibold"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="section-description">Mô tả</Label>
                <Textarea
                  id="section-description"
                  value={sectionConfig.sectionDescription}
                  onChange={(e) => setSectionConfig({ ...sectionConfig, sectionDescription: e.target.value })}
                  rows={3}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="badge-text">Text badge</Label>
                <Input
                  id="badge-text"
                  value={sectionConfig.badgeText}
                  onChange={(e) => setSectionConfig({ ...sectionConfig, badgeText: e.target.value })}
                />
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="news" className="space-y-6">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle>Quản lý tin tức</CardTitle>
                  <CardDescription>Danh sách tin tức hiển thị trên trang chủ</CardDescription>
                </div>
                <Button onClick={handleCreateArticle}>
                  <Plus className="w-4 h-4 mr-2" />
                  Thêm tin tức
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {articles.map((article) => (
                  <div key={article.id} className="flex items-center justify-between p-4 border rounded-lg">
                    <div className="flex-1">
                      <div className="flex items-center gap-3 mb-2">
                        <h4 className="font-medium">{article.title}</h4>
                        {article.is_featured && (
                          <Badge className="bg-gradient-to-r from-red-500 to-pink-500 text-white">
                            <Star className="w-3 h-3 mr-1" />
                            Nổi bật
                          </Badge>
                        )}
                      </div>
                      <p className="text-sm text-muted-foreground mb-2 line-clamp-1">
                        {article.excerpt}
                      </p>
                      <div className="flex items-center gap-3 flex-wrap">
                        <Badge variant="outline">{article.category}</Badge>
                        <Badge className={getStatusColor(article.is_published ? "Đã xuất bản" : "Bản nháp")}>
                          {article.is_published ? "Đã xuất bản" : "Bản nháp"}
                        </Badge>
                        <div className="flex items-center gap-1 text-xs text-muted-foreground">
                          <User className="w-3 h-3" />
                          {article.author}
                        </div>
                        <div className="flex items-center gap-1 text-xs text-muted-foreground">
                          <Calendar className="w-3 h-3" />
                          {article.publish_date ? new Date(article.publish_date).toLocaleDateString() : "Chưa xuất bản"}
                        </div>
                        <div className="flex items-center gap-1 text-xs text-muted-foreground">
                          <Eye className="w-3 h-3" />
                          {article.views || 0} lượt xem
                        </div>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <Button variant="outline" size="sm" onClick={() => handleEditArticle(article)}>
                        <Edit className="w-4 h-4" />
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        className="text-red-600 hover:text-red-700"
                        onClick={() => handleDeleteArticle(article.id)}
                      >
                        <Trash className="w-4 h-4" />
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="categories" className="space-y-6">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="flex items-center gap-2">
                    <Tag className="w-5 h-5" />
                    Danh mục tin tức
                  </CardTitle>
                  <CardDescription>Quản lý các danh mục tin tức</CardDescription>
                </div>
                <Button onClick={() => setCategoryDialogOpen(true)}>
                  <Plus className="w-4 h-4 mr-2" />
                  Thêm danh mục
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {categories.map((category, index) => (
                  <div key={index} className="flex items-center justify-between p-4 border rounded-lg">
                    <div>
                      <div className="font-medium">{category.name}</div>
                      <div className="text-sm text-muted-foreground">{category.count} bài viết</div>
                    </div>
                    <div className="flex items-center gap-2">
                      <Button variant="outline" size="sm">
                        <Edit className="w-4 h-4" />
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        className="text-red-600 hover:text-red-700"
                        onClick={() => handleDeleteCategory(category.name)}
                      >
                        <Trash className="w-4 h-4" />
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Create/Edit Article Dialog */}
      <Dialog open={dialogOpen} onOpenChange={setDialogOpen}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>
              {editingArticle ? "Chỉnh sửa bài viết" : "Tạo bài viết mới"}
            </DialogTitle>
            <DialogDescription>
              Sử dụng Markdown để định dạng nội dung bài viết
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="title">Tiêu đề</Label>
                <Input
                  id="title"
                  value={formData.title}
                  onChange={(e) => setFormData({ ...formData, title: e.target.value })}
                  placeholder="Nhập tiêu đề bài viết..."
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="category">Danh mục</Label>
                <Select value={formData.category} onValueChange={(value) => setFormData({ ...formData, category: value })}>
                  <SelectTrigger>
                    <SelectValue placeholder="Chọn danh mục" />
                  </SelectTrigger>
                  <SelectContent>
                    {categories.map(cat => (
                      <SelectItem key={cat.name} value={cat.name}>{cat.name}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="excerpt">Tóm tắt</Label>
              <Textarea
                id="excerpt"
                value={formData.excerpt}
                onChange={(e) => setFormData({ ...formData, excerpt: e.target.value })}
                placeholder="Nhập tóm tắt ngắn gọn..."
                rows={2}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="tags">Tags (phân cách bằng dấu phẩy)</Label>
              <Input
                id="tags"
                value={formData.tags}
                onChange={(e) => setFormData({ ...formData, tags: e.target.value })}
                placeholder="vd: taxi, ưu đãi, phú quốc"
              />
            </div>

            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <Switch
                  checked={formData.featured}
                  onCheckedChange={(checked) => setFormData({ ...formData, featured: checked })}
                />
                <Label>Bài viết nổi bật</Label>
              </div>

              <div className="space-y-2">
                <Label htmlFor="status">Trạng thái</Label>
                <Select value={formData.status} onValueChange={(value: any) => setFormData({ ...formData, status: value })}>
                  <SelectTrigger className="w-40">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Bản nháp">Bản nháp</SelectItem>
                    <SelectItem value="Đang xem xét">Đang xem xét</SelectItem>
                    <SelectItem value="Đã xuất bản">Đã xuất bản</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="space-y-2">
              <Label>Nội dung (Markdown)</Label>
              <div data-color-mode="light">
                <MDEditor
                  value={markdownContent}
                  onChange={(val) => setMarkdownContent(val || "")}
                  height={400}
                  preview="edit"
                />
              </div>
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setDialogOpen(false)}>
              Hủy
            </Button>
            <Button onClick={handleSaveArticle}>
              {editingArticle ? "Cập nhật" : "Tạo mới"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Preview Modal */}
      <PreviewModal
        isOpen={showPreview}
        onClose={() => setShowPreview(false)}
        title="Tin tức & Khuyến mãi"
      >
        <NewsPreview config={{ ...sectionConfig, articles: articles }} />
      </PreviewModal>

      {/* Add Category Dialog */}
      <Dialog open={categoryDialogOpen} onOpenChange={setCategoryDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Thêm danh mục mới</DialogTitle>
            <DialogDescription>
              Tạo danh mục mới cho tin tức
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="categoryName">Tên danh mục</Label>
              <Input
                id="categoryName"
                value={newCategoryName}
                onChange={(e) => setNewCategoryName(e.target.value)}
                placeholder="Nhập tên danh mục..."
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setCategoryDialogOpen(false)}>
              Hủy
            </Button>
            <Button onClick={handleAddCategory}>
              Thêm danh mục
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default NewsEdit;