import { Phone, Mail, MapPin, Clock, Facebook, Zap, Instagram, Youtube, Award, Shield, Users, Star, CheckCircle, Car, Route, Globe, Send } from "lucide-react";
import { ZaloIcon } from "@/components/ui/zalo-icon";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { useSupabaseContent } from "@/hooks/useSupabaseContent";
import { useState, useEffect } from "react";

const Footer = () => {
  const { data: footerData, loading: footerLoading } = useSupabaseContent('footer');



  // Default fallback configuration
  const defaultFooterConfig = {
    company: {
      name: "BICOM",
      description: "Dịch vụ taxi uy tín và chuyên nghiệp tại TP. Hồ Chí Minh. Chúng tôi cam kết mang đến cho khách hàng những chuyến đi an toàn, tho<PERSON>i mái với gi<PERSON> c<PERSON> hợ<PERSON> lý."
    },
    contact: {
      address: "T<PERSON><PERSON> <PERSON><PERSON>, Việt Nam",
      phone: "0823141862",
      email: "<EMAIL>",
      website: "www.bicom.com"
    },
    services: [
      "Taxi trong thành phố",
      "Taxi đường dài",
      "Taxi hợp đồng",
      "Taxi sân bay"
    ],
    socialLinks: [
      { name: "Facebook", url: "https://facebook.com/bicom", icon: "facebook" },
      { name: "Zalo", url: "https://zalo.me/0823141862", icon: "zalo" },
      { name: "Phone", url: "tel:0823141862", icon: "phone" }
    ],
    copyright: "© 2024 BICOM. Tất cả quyền được bảo lưu.",
    additionalInfo: "CÔNG TY TNHH SẢN XUẤT VÀ THƯƠNG MẠI DỊCH VỤ BICOM - Mã số thuế: 0315360616"
  };

  // State for footer config
  const [footerConfig, setFooterConfig] = useState(defaultFooterConfig);

  // Update state when data loads from database
  useEffect(() => {
    if (footerData?.content) {
      // Merge database data with default config to preserve missing fields
      const mergedConfig = {
        ...defaultFooterConfig,
        ...footerData.content,
        // Ensure nested objects are properly merged
        company: {
          ...defaultFooterConfig.company,
          ...footerData.content.company
        },
        contact: {
          ...defaultFooterConfig.contact,
          ...footerData.content.contact
        },
        // Use default additionalInfo if database value is empty
        additionalInfo: footerData.content.additionalInfo || defaultFooterConfig.additionalInfo
      };
      setFooterConfig(mergedConfig);
    } else if (!footerLoading) {
      setFooterConfig(defaultFooterConfig);
    }
  }, [footerData, footerLoading]);

  const quickLinks = [
    { name: "Giới thiệu", href: "#gioithieu", icon: Users },
    { name: "Dịch vụ", href: "#dichvu", icon: Car },
    { name: "Bảng giá", href: "#banggia", icon: Star },
    { name: "Tin tức", href: "#tintuc", icon: Globe },
    { name: "Liên hệ", href: "#lienhe", icon: Phone }
  ];

  const achievements = [
    { icon: Award, title: "15+ năm", subtitle: "Kinh nghiệm" },
    { icon: Users, title: "50.000+", subtitle: "Khách hàng" },
    { icon: Car, title: "200+", subtitle: "Xe taxi" },
    { icon: Shield, title: "100%", subtitle: "An toàn" }
  ];

  const certifications = [
    "Giấy phép kinh doanh taxi",
    "Chứng chỉ ISO 9001:2015",
    "Bảo hiểm trách nhiệm nghề nghiệp",
    "Chứng nhận doanh nghiệp uy tín"
  ];

  return (
    <footer id="lienhe" className="relative overflow-hidden">
      {/* Background decoration */}
      <div className="absolute inset-0 bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900"></div>
      <div className="absolute top-0 left-0 w-full h-2 bg-gradient-primary"></div>
      <div className="absolute top-20 left-20 w-72 h-72 bg-primary/10 rounded-full blur-3xl"></div>
      <div className="absolute bottom-20 right-20 w-96 h-96 bg-accent/10 rounded-full blur-3xl"></div>


      {/* Main Footer */}
      <div className="relative z-10 text-white py-16">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-8">
            {/* Company Info */}
            <div className="lg:col-span-2">
              <div className="flex items-center space-x-3 mb-6">
                <img
                  src="/bicom-now-logo.svg"
                  alt="Bicom Now Logo"
                  className="h-16 w-auto filter brightness-0 invert"
                />
                <div>
                  <p className="text-primary font-medium">Sản xuất và Thương mại Dịch vụ</p>
                </div>
              </div>

              <p className="text-gray-300 mb-6 leading-relaxed">
                {footerConfig.company.description}
              </p>

              {/* Social Media */}
              <div className="flex space-x-3 mb-6">
                {footerConfig.socialLinks.map((link, index) => {
                  // Map icon names to components
                  const getIconComponent = (iconName) => {
                    switch (iconName) {
                      case 'facebook':
                        return <Facebook className="w-4 h-4" />;
                      case 'instagram':
                        return <Instagram className="w-4 h-4" />;
                      case 'zalo':
                        return <ZaloIcon className="w-4 h-4" />;
                      case 'phone':
                        return <Phone className="w-4 h-4" />;
                      default:
                        return <Facebook className="w-4 h-4" />;
                    }
                  };

                  // Map icon names to colors
                  const getButtonStyle = (iconName) => {
                    switch (iconName) {
                      case 'facebook':
                        return "bg-blue-600 hover:bg-blue-700 border-0";
                      case 'instagram':
                        return "bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 border-0";
                      case 'zalo':
                        return "bg-blue-500 hover:bg-blue-600 border-0";
                      case 'phone':
                        return "bg-green-600 hover:bg-green-700 border-0";
                      default:
                        return "bg-gray-600 hover:bg-gray-700 border-0";
                    }
                  };

                  return (
                    <Button
                      key={index}
                      size="icon"
                      className={getButtonStyle(link.icon)}
                      onClick={() => window.open(link.url, '_blank')}
                    >
                      {getIconComponent(link.icon)}
                    </Button>
                  );
                })}
              </div>

              {/* Certifications */}
              <div>
                <h5 className="font-semibold text-white mb-3">Chứng nhận & Giấy phép</h5>
                <div className="space-y-2">
                  {certifications.map((cert, index) => (
                    <div key={index} className="flex items-center gap-2 text-sm text-gray-300">
                      <CheckCircle className="w-4 h-4 text-primary flex-shrink-0" />
                      {cert}
                    </div>
                  ))}
                </div>
              </div>
            </div>

            {/* Quick Links */}
            <div>
              <h4 className="font-bold text-lg mb-6 text-white">Liên kết nhanh</h4>
              <ul className="space-y-3">
                {quickLinks.map((link) => (
                  <li key={link.name}>
                    <a
                      href={link.href}
                      onClick={(e) => {
                        e.preventDefault();
                        const targetId = link.href.replace('#', '');
                        const targetElement = document.getElementById(targetId);
                        if (targetElement) {
                          targetElement.scrollIntoView({ behavior: 'smooth' });
                        }
                      }}
                      className="group flex items-center gap-3 text-gray-300 hover:text-primary transition-all duration-300 py-1 cursor-pointer"
                    >
                      <div className="w-8 h-8 bg-gray-800 rounded-lg flex items-center justify-center group-hover:bg-primary transition-colors duration-300">
                        <link.icon className="w-4 h-4" />
                      </div>
                      <span className="group-hover:translate-x-1 transition-transform duration-300">
                        {link.name}
                      </span>
                    </a>
                  </li>
                ))}
              </ul>
            </div>

            {/* Services */}
            <div>
              <h4 className="font-bold text-lg mb-6 text-white">Dịch vụ & Giá</h4>
              <ul className="space-y-3">
                {footerConfig.services.map((service, index) => (
                  <li key={index} className="group">
                    <div className="flex items-center gap-2">
                      <CheckCircle className="w-3 h-3 text-primary" />
                      <span className="text-gray-300 text-sm group-hover:text-white transition-colors">
                        {service}
                      </span>
                    </div>
                  </li>
                ))}
              </ul>
            </div>

            {/* Contact Info */}
            <div>
              <h4 className="font-bold text-lg mb-6 text-white">Liên hệ 24/7</h4>
              <div className="space-y-4">
                <div className="bg-white/5 backdrop-blur-sm rounded-xl p-4 border border-white/10">
                  <div className="flex items-start space-x-3">
                    <div className="w-10 h-10 bg-gradient-primary rounded-lg flex items-center justify-center flex-shrink-0">
                      <Phone className="w-5 h-5 text-white" />
                    </div>
                    <div>
                      <p className="text-white font-semibold">Hotline 24/7</p>
                      <p className="text-primary text-xl font-bold">
                        {footerConfig.contact.phone}
                      </p>
                      <p className="text-xs text-gray-400">Miễn phí cuộc gọi</p>
                    </div>
                  </div>
                </div>

                <div className="space-y-3">
                  <div className="flex items-start space-x-3">
                    <Mail className="w-5 h-5 text-primary mt-1 flex-shrink-0" />
                    <div>
                      <p className="text-gray-300 text-sm">Email</p>
                      <p className="text-white">
                        {footerConfig.contact.email}
                      </p>
                    </div>
                  </div>

                  <div className="flex items-start space-x-3">
                    <MapPin className="w-5 h-5 text-primary mt-1 flex-shrink-0" />
                    <div>
                      <p className="text-gray-300 text-sm">Địa chỉ</p>
                      <p className="text-white">
                        {footerConfig.contact.address}
                      </p>
                    </div>
                  </div>

                  <div className="flex items-start space-x-3">
                    <Clock className="w-5 h-5 text-primary mt-1 flex-shrink-0" />
                    <div>
                      <p className="text-gray-300 text-sm">Giờ hoạt động</p>
                      <p className="text-white">24/7 - Tất cả các ngày</p>
                    </div>
                  </div>

                  {/* Website */}
                  {footerConfig.contact.website && (
                    <div className="flex items-start space-x-3">
                      <Globe className="w-5 h-5 text-primary mt-1 flex-shrink-0" />
                      <div>
                        <p className="text-gray-300 text-sm">Website</p>
                        <p className="text-white">
                          {footerConfig.contact.website}
                        </p>
                      </div>
                    </div>
                  )}
                </div>

                {/* Newsletter */}
                <div className="bg-gradient-to-r from-primary/20 to-accent/20 rounded-xl p-4 border border-primary/20">
                  <h5 className="font-semibold text-white mb-2">Nhận tin khuyến mãi</h5>
                  <div className="flex gap-2">
                    <input
                      type="email"
                      placeholder="Email của bạn"
                      className="flex-1 px-3 py-2 bg-white/10 border border-white/20 rounded-lg text-white placeholder-gray-400 text-sm focus:outline-none focus:ring-2 focus:ring-primary"
                    />
                    <Button size="sm" className="bg-gradient-primary hover:bg-primary-hover">
                      <Send className="w-4 h-4" />
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Bottom Footer */}
      <div className="relative z-10 border-t border-gray-700 bg-gray-900/50 backdrop-blur-sm">
        <div className="container mx-auto px-4 py-6">
          <div className="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
            <div className="text-gray-400 text-sm">
              <div>{footerConfig.copyright || "© 2024 BICOM. Tất cả quyền được bảo lưu."}</div>
              {footerConfig.additionalInfo && (
                <div className="text-xs text-gray-500 mt-1">
                  {footerConfig.additionalInfo}
                </div>
              )}
            </div>
            <div className="flex flex-wrap gap-6 text-sm text-gray-400">
              <a href="/terms" className="hover:text-primary transition-colors">Điều khoản sử dụng</a>
              <a href="/privacy" className="hover:text-primary transition-colors">Chính sách bảo mật</a>
              <a href="/contact" className="hover:text-primary transition-colors">Hỗ trợ khách hàng</a>
              <a href="/careers" className="hover:text-primary transition-colors">Tuyển dụng</a>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;