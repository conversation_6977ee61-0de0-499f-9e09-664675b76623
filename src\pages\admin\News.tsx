import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { Plus, Edit, Trash2, Eye, Calendar } from "lucide-react";
import { toast } from "sonner";
import { useNews } from "@/hooks/useNews";
import { format } from "date-fns";
import { vi } from "date-fns/locale";

const News = () => {
  const {
    articles,
    loading,
    createArticle,
    updateArticle,
    deleteArticle
  } = useNews({ published: undefined }); // Get all articles including drafts
  const [filter, setFilter] = useState("all");
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [editingNews, setEditingNews] = useState<any>(null);
  const [newNews, setNewNews] = useState({
    title: "",
    excerpt: "",
    content: "",
    author: "Nguyễn Văn Admin",
    category: "Tin mới",
    is_published: false,
    is_featured: false,
    featured_image: "/placeholder.svg"
  });

  const filteredNews = articles.filter(item => {
    if (filter === "all") return true;
    if (filter === "published") return item.is_published;
    if (filter === "draft") return !item.is_published;
    return true;
  });

  const categories = ["Tin mới", "Khuyến mãi", "Hướng dẫn", "Thông báo"];

  const formatDate = (dateString: string) => {
    try {
      return format(new Date(dateString), "dd/MM/yyyy", { locale: vi });
    } catch {
      return dateString;
    }
  };

  const handleCreate = async () => {
    try {
      const { error } = await createArticle({
        title: newNews.title,
        excerpt: newNews.excerpt,
        content: newNews.content,
        author: newNews.author,
        category: newNews.category,
        featured_image: newNews.featured_image,
        is_featured: newNews.is_featured,
        is_published: newNews.is_published,
        slug: "", // Will be generated automatically
        views: 0,
        publish_date: newNews.is_published ? new Date().toISOString() : null,
        created_at: "",
        updated_at: ""
      });

      if (error) {
        toast.error(`Lỗi: ${error}`);
        return;
      }

      setNewNews({
        title: "",
        excerpt: "",
        content: "",
        author: "Nguyễn Văn Admin",
        category: "Tin mới",
        is_published: false,
        is_featured: false,
        featured_image: "/placeholder.svg"
      });
      setIsCreateDialogOpen(false);
      toast.success("Đã tạo bài viết mới");
    } catch (error) {
      toast.error("Có lỗi xảy ra khi tạo bài viết");
    }
  };

  const handleEdit = (item: any) => {
    setEditingNews({ ...item });
    setIsEditDialogOpen(true);
  };

  const handleSaveEdit = () => {
    if (!editingNews) return;
    // Update will be handled by the hook
    // setNews(news.map(n =>
    //   n.id === editingNews.id ? editingNews : n
    // ));
    setIsEditDialogOpen(false);
    setEditingNews(null);
    toast.success("Đã cập nhật bài viết");
  };

  const handleDelete = async (id: string) => {
    try {
      const { error } = await deleteArticle(id);
      if (error) {
        toast.error(`Lỗi: ${error}`);
        return;
      }
      toast.success("Đã xóa bài viết");
    } catch (error) {
      toast.error("Có lỗi xảy ra khi xóa bài viết");
    }
  };

  const handleToggleStatus = async (id: string) => {
    try {
      const article = articles.find(a => a.id === id);
      if (!article) return;

      const { error } = await updateArticle(id, {
        ...article,
        is_published: !article.is_published
      });

      if (error) {
        toast.error(`Lỗi: ${error}`);
        return;
      }
      toast.success("Đã cập nhật trạng thái");
    } catch (error) {
      toast.error("Có lỗi xảy ra khi cập nhật");
    }
  };

  const handleToggleFeatured = async (id: string) => {
    try {
      const article = articles.find(a => a.id === id);
      if (!article) return;

      const { error } = await updateArticle(id, {
        ...article,
        is_featured: !article.is_featured
      });

      if (error) {
        toast.error(`Lỗi: ${error}`);
        return;
      }
      toast.success("Đã cập nhật tin nổi bật");
    } catch (error) {
      toast.error("Có lỗi xảy ra khi cập nhật");
    }
  };

  return (
    <div className="p-6 space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold">Quản lý Tin tức</h1>
          <p className="text-muted-foreground">Quản lý bài viết và tin tức công ty</p>
        </div>
        <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
          <DialogTrigger asChild>
            <Button className="flex items-center gap-2">
              <Plus className="h-4 w-4" />
              Viết bài mới
            </Button>
          </DialogTrigger>
          <DialogContent className="sm:max-w-[700px]">
            <DialogHeader>
              <DialogTitle>Tạo bài viết mới</DialogTitle>
              <DialogDescription>
                Viết bài viết tin tức cho website
              </DialogDescription>
            </DialogHeader>
            <div className="grid gap-4 py-4 max-h-96 overflow-y-auto">
              <div className="space-y-2">
                <Label htmlFor="title">Tiêu đề</Label>
                <Input
                  id="title"
                  value={newNews.title}
                  onChange={(e) => setNewNews({ ...newNews, title: e.target.value })}
                  placeholder="Nhập tiêu đề bài viết"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="excerpt">Tóm tắt</Label>
                <Textarea
                  id="excerpt"
                  value={newNews.excerpt}
                  onChange={(e) => setNewNews({ ...newNews, excerpt: e.target.value })}
                  placeholder="Tóm tắt ngắn gọn về bài viết"
                  rows={2}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="content">Nội dung</Label>
                <Textarea
                  id="content"
                  value={newNews.content}
                  onChange={(e) => setNewNews({ ...newNews, content: e.target.value })}
                  placeholder="Nội dung chi tiết bài viết"
                  rows={6}
                />
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="category">Danh mục</Label>
                  <Select value={newNews.category} onValueChange={(value) => setNewNews({ ...newNews, category: value })}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {categories.map(cat => (
                        <SelectItem key={cat} value={cat}>{cat}</SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="status">Trạng thái</Label>
                  <Select value={newNews.status} onValueChange={(value) => setNewNews({ ...newNews, status: value as typeof newNews.status })}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="draft">Bản nháp</SelectItem>
                      <SelectItem value="published">Xuất bản</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
              <div className="flex items-center space-x-2">
                <Switch
                  id="featured"
                  checked={newNews.featured}
                  onCheckedChange={(checked) => setNewNews({ ...newNews, featured: checked })}
                />
                <Label htmlFor="featured">Tin nổi bật</Label>
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setIsCreateDialogOpen(false)}>
                Hủy
              </Button>
              <Button onClick={handleCreate}>
                Tạo bài viết
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>

      <div className="flex gap-2">
        <Button
          variant={filter === "all" ? "default" : "outline"}
          size="sm"
          onClick={() => setFilter("all")}
        >
          Tất cả ({articles.length})
        </Button>
        <Button
          variant={filter === "published" ? "default" : "outline"}
          size="sm"
          onClick={() => setFilter("published")}
        >
          Đã xuất bản ({articles.filter(n => n.is_published).length})
        </Button>
        <Button
          variant={filter === "draft" ? "default" : "outline"}
          size="sm"
          onClick={() => setFilter("draft")}
        >
          Bản nháp ({articles.filter(n => !n.is_published).length})
        </Button>
      </div>

      <div className="grid gap-4">
        {filteredNews.map((item) => (
          <Card key={item.id}>
            <CardHeader>
              <div className="flex items-start justify-between">
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <CardTitle className="text-lg">{item.title}</CardTitle>
                    {item.featured && (
                      <Badge variant="default" className="bg-yellow-500">
                        Nổi bật
                      </Badge>
                    )}
                  </div>
                  <div className="flex items-center gap-4 text-sm text-muted-foreground">
                    <span className="flex items-center gap-1">
                      <Calendar className="h-4 w-4" />
                      {item.publishDate}
                    </span>
                    <span>{item.author}</span>
                    <Badge variant="outline">{item.category}</Badge>
                    <Badge
                      variant={item.status === 'published' ? 'default' : 'secondary'}
                    >
                      {item.status === 'published' ? 'Đã xuất bản' : 'Bản nháp'}
                    </Badge>
                  </div>
                </div>

                <div className="flex gap-2">
                  <Button size="sm" variant="outline">
                    <Eye className="h-4 w-4 mr-1" />
                    {item.views}
                  </Button>
                  <Button size="sm" variant="outline" onClick={() => handleEdit(item)}>
                    <Edit className="h-4 w-4" />
                  </Button>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => handleToggleStatus(item.id)}
                  >
                    {item.status === 'published' ? 'Ẩn' : 'Xuất bản'}
                  </Button>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => handleToggleFeatured(item.id)}
                  >
                    {item.featured ? 'Bỏ nổi bật' : 'Nổi bật'}
                  </Button>
                  <Button
                    size="sm"
                    variant="destructive"
                    onClick={() => handleDelete(item.id)}
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </CardHeader>

            <CardContent>
              <CardDescription className="line-clamp-2">
                {item.excerpt}
              </CardDescription>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Edit Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="sm:max-w-[700px]">
          <DialogHeader>
            <DialogTitle>Chỉnh sửa bài viết</DialogTitle>
            <DialogDescription>
              Cập nhật thông tin bài viết tin tức
            </DialogDescription>
          </DialogHeader>
          {editingNews && (
            <div className="grid gap-4 py-4 max-h-96 overflow-y-auto">
              <div className="space-y-2">
                <Label htmlFor="edit-title">Tiêu đề</Label>
                <Input
                  id="edit-title"
                  value={editingNews.title}
                  onChange={(e) => setEditingNews({ ...editingNews, title: e.target.value })}
                  placeholder="Nhập tiêu đề bài viết"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="edit-excerpt">Tóm tắt</Label>
                <Textarea
                  id="edit-excerpt"
                  value={editingNews.excerpt}
                  onChange={(e) => setEditingNews({ ...editingNews, excerpt: e.target.value })}
                  placeholder="Tóm tắt ngắn gọn về bài viết"
                  rows={2}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="edit-content">Nội dung</Label>
                <Textarea
                  id="edit-content"
                  value={editingNews.content}
                  onChange={(e) => setEditingNews({ ...editingNews, content: e.target.value })}
                  placeholder="Nội dung chi tiết bài viết"
                  rows={6}
                />
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="edit-category">Danh mục</Label>
                  <Select value={editingNews.category} onValueChange={(value) => setEditingNews({ ...editingNews, category: value })}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {categories.map(cat => (
                        <SelectItem key={cat} value={cat}>{cat}</SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="edit-status">Trạng thái</Label>
                  <Select value={editingNews.status} onValueChange={(value) => setEditingNews({ ...editingNews, status: value })}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="draft">Bản nháp</SelectItem>
                      <SelectItem value="published">Xuất bản</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
              <div className="flex items-center space-x-2">
                <Switch
                  id="edit-featured"
                  checked={editingNews.featured}
                  onCheckedChange={(checked) => setEditingNews({ ...editingNews, featured: checked })}
                />
                <Label htmlFor="edit-featured">Tin nổi bật</Label>
              </div>
            </div>
          )}
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsEditDialogOpen(false)}>
              Hủy
            </Button>
            <Button onClick={handleSaveEdit}>
              Lưu thay đổi
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default News;