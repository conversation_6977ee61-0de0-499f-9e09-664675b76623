import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Plus, Edit, Trash2, Star, DollarSign, Car, Users } from "lucide-react";
import { toast } from "sonner";
import { usePricing } from "@/hooks/usePricing";

const Pricing = () => {
  const {
    plans,
    loading,
    createPlan,
    updatePlan,
    deletePlan,
    togglePopular
  } = usePricing();

  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [editingPlan, setEditingPlan] = useState<any>(null);
  const [newPlan, setNewPlan] = useState({
    plan_id: "",
    title: "",
    description: "",
    icon_type: "car",
    is_popular: false,
    prices: [{ distance: "", price: "", unit: "" }],
    features: [""],
    is_active: true,
    display_order: 0
  });

  const handleCreate = async () => {
    try {
      const { error } = await createPlan(newPlan);
      if (error) {
        toast.error(`Lỗi: ${error}`);
        return;
      }
      
      setNewPlan({
        plan_id: "",
        title: "",
        description: "",
        icon_type: "car",
        is_popular: false,
        prices: [{ distance: "", price: "", unit: "" }],
        features: [""],
        is_active: true,
        display_order: 0
      });
      setIsCreateDialogOpen(false);
      toast.success("Đã tạo bảng giá mới");
    } catch (error) {
      toast.error("Có lỗi xảy ra khi tạo bảng giá");
    }
  };

  const handleEdit = (plan: any) => {
    setEditingPlan(plan);
    setIsEditDialogOpen(true);
  };

  const handleUpdate = async () => {
    try {
      const { error } = await updatePlan(editingPlan.id, editingPlan);
      if (error) {
        toast.error(`Lỗi: ${error}`);
        return;
      }
      
      setEditingPlan(null);
      setIsEditDialogOpen(false);
      toast.success("Đã cập nhật bảng giá");
    } catch (error) {
      toast.error("Có lỗi xảy ra khi cập nhật");
    }
  };

  const handleDelete = async (id: string) => {
    if (!confirm("Bạn có chắc muốn xóa bảng giá này?")) return;
    
    try {
      const { error } = await deletePlan(id);
      if (error) {
        toast.error(`Lỗi: ${error}`);
        return;
      }
      toast.success("Đã xóa bảng giá");
    } catch (error) {
      toast.error("Có lỗi xảy ra khi xóa");
    }
  };

  const handleTogglePopular = async (id: string) => {
    try {
      const { error } = await togglePopular(id);
      if (error) {
        toast.error(`Lỗi: ${error}`);
        return;
      }
      toast.success("Đã cập nhật trạng thái phổ biến");
    } catch (error) {
      toast.error("Có lỗi xảy ra");
    }
  };

  const addPriceRow = (isEdit = false) => {
    const newPrice = { distance: "", price: "", unit: "" };
    if (isEdit && editingPlan) {
      setEditingPlan({
        ...editingPlan,
        prices: [...editingPlan.prices, newPrice]
      });
    } else {
      setNewPlan({
        ...newPlan,
        prices: [...newPlan.prices, newPrice]
      });
    }
  };

  const addFeature = (isEdit = false) => {
    if (isEdit && editingPlan) {
      setEditingPlan({
        ...editingPlan,
        features: [...editingPlan.features, ""]
      });
    } else {
      setNewPlan({
        ...newPlan,
        features: [...newPlan.features, ""]
      });
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Quản lý bảng giá</h1>
          <p className="text-muted-foreground">Quản lý các gói giá dịch vụ taxi</p>
        </div>
        
        <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="w-4 h-4 mr-2" />
              Thêm bảng giá
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle>Tạo bảng giá mới</DialogTitle>
              <DialogDescription>
                Thêm gói giá dịch vụ mới
              </DialogDescription>
            </DialogHeader>
            
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label>ID bảng giá</Label>
                  <Input
                    value={newPlan.plan_id}
                    onChange={(e) => setNewPlan({ ...newPlan, plan_id: e.target.value })}
                    placeholder="4seats, 7seats..."
                  />
                </div>
                <div>
                  <Label>Tiêu đề</Label>
                  <Input
                    value={newPlan.title}
                    onChange={(e) => setNewPlan({ ...newPlan, title: e.target.value })}
                    placeholder="BẢNG GIÁ XE 4 CHỖ"
                  />
                </div>
              </div>
              
              <div>
                <Label>Mô tả</Label>
                <Textarea
                  value={newPlan.description}
                  onChange={(e) => setNewPlan({ ...newPlan, description: e.target.value })}
                  placeholder="Mô tả về gói giá..."
                />
              </div>
              
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label>Icon</Label>
                  <Select value={newPlan.icon_type} onValueChange={(value) => setNewPlan({ ...newPlan, icon_type: value })}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="car">Car</SelectItem>
                      <SelectItem value="users">Users</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    checked={newPlan.is_popular}
                    onChange={(e) => setNewPlan({ ...newPlan, is_popular: e.target.checked })}
                  />
                  <Label>Phổ biến</Label>
                </div>
              </div>
              
              <div>
                <Label>Bảng giá</Label>
                {newPlan.prices.map((price, index) => (
                  <div key={index} className="grid grid-cols-3 gap-2 mt-2">
                    <Input
                      placeholder="Quãng đường"
                      value={price.distance}
                      onChange={(e) => {
                        const newPrices = [...newPlan.prices];
                        newPrices[index].distance = e.target.value;
                        setNewPlan({ ...newPlan, prices: newPrices });
                      }}
                    />
                    <Input
                      placeholder="Giá"
                      value={price.price}
                      onChange={(e) => {
                        const newPrices = [...newPlan.prices];
                        newPrices[index].price = e.target.value;
                        setNewPlan({ ...newPlan, prices: newPrices });
                      }}
                    />
                    <Input
                      placeholder="Đơn vị"
                      value={price.unit}
                      onChange={(e) => {
                        const newPrices = [...newPlan.prices];
                        newPrices[index].unit = e.target.value;
                        setNewPlan({ ...newPlan, prices: newPrices });
                      }}
                    />
                  </div>
                ))}
                <Button type="button" variant="outline" onClick={() => addPriceRow()} className="mt-2">
                  Thêm giá
                </Button>
              </div>
              
              <div>
                <Label>Tính năng</Label>
                {newPlan.features.map((feature, index) => (
                  <Input
                    key={index}
                    className="mt-2"
                    placeholder="Tính năng..."
                    value={feature}
                    onChange={(e) => {
                      const newFeatures = [...newPlan.features];
                      newFeatures[index] = e.target.value;
                      setNewPlan({ ...newPlan, features: newFeatures });
                    }}
                  />
                ))}
                <Button type="button" variant="outline" onClick={() => addFeature()} className="mt-2">
                  Thêm tính năng
                </Button>
              </div>
            </div>
            
            <div className="flex justify-end space-x-2">
              <Button variant="outline" onClick={() => setIsCreateDialogOpen(false)}>
                Hủy
              </Button>
              <Button onClick={handleCreate}>
                Tạo bảng giá
              </Button>
            </div>
          </DialogContent>
        </Dialog>
      </div>

      <div className="grid gap-4">
        {plans.map((plan) => (
          <Card key={plan.id}>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className="w-10 h-10 rounded-lg bg-primary/10 flex items-center justify-center">
                    {plan.icon_type === 'car' ? <Car className="w-5 h-5 text-primary" /> : <Users className="w-5 h-5 text-primary" />}
                  </div>
                  <div>
                    <CardTitle className="text-lg">{plan.title}</CardTitle>
                    <CardDescription>{plan.description}</CardDescription>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  {plan.is_popular && (
                    <Badge variant="secondary">
                      <Star className="w-3 h-3 mr-1" />
                      Phổ biến
                    </Badge>
                  )}
                  <Badge variant={plan.is_active ? "default" : "secondary"}>
                    {plan.is_active ? "Hoạt động" : "Tạm dừng"}
                  </Badge>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <h4 className="font-medium mb-2">Bảng giá:</h4>
                  <div className="space-y-1">
                    {plan.prices?.map((price: any, index: number) => (
                      <div key={index} className="flex justify-between text-sm">
                        <span>{price.distance}</span>
                        <span className="font-medium">{price.price} {price.unit}</span>
                      </div>
                    ))}
                  </div>
                </div>
                
                <div>
                  <h4 className="font-medium mb-2">Tính năng:</h4>
                  <ul className="text-sm space-y-1">
                    {plan.features?.map((feature: string, index: number) => (
                      <li key={index} className="flex items-start gap-2">
                        <span className="text-green-500 mt-0.5">•</span>
                        {feature}
                      </li>
                    ))}
                  </ul>
                </div>
                
                <div className="flex justify-end gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleTogglePopular(plan.id)}
                  >
                    <Star className="w-4 h-4 mr-1" />
                    {plan.is_popular ? "Bỏ phổ biến" : "Đặt phổ biến"}
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleEdit(plan)}
                  >
                    <Edit className="w-4 h-4 mr-1" />
                    Sửa
                  </Button>
                  <Button
                    variant="destructive"
                    size="sm"
                    onClick={() => handleDelete(plan.id)}
                  >
                    <Trash2 className="w-4 h-4 mr-1" />
                    Xóa
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
};

export default Pricing;
