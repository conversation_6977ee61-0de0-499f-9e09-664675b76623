import { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';

export interface ContentSection {
  id: string;
  section_key: string;
  title: string;
  description: string;
  content: any;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export const useSupabaseContent = (sectionKey: string) => {
  const [data, setData] = useState<ContentSection | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchContent = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const { data: contentData, error: fetchError } = await supabase
        .from('content_sections')
        .select('*')
        .eq('section_key', sectionKey)
        .eq('is_active', true)
        .single();
      
      if (fetchError) {
        if (fetchError.code === 'PGRST116') {
          // No rows found - this is expected for new sections
          setData(null);
        } else {
          throw fetchError;
        }
      } else {
        setData(contentData);
      }
    } catch (err: any) {
      console.error(`Error fetching content for ${sectionKey}:`, err);
      setError(err.message || 'Failed to fetch content');
    } finally {
      setLoading(false);
    }
  };

  const updateContent = async (updates: Partial<ContentSection>) => {
    try {
      const { error: updateError } = await supabase
        .from('content_sections')
        .update({
          ...updates,
          updated_at: new Date().toISOString()
        })
        .eq('section_key', sectionKey);
      
      if (updateError) throw updateError;
      
      // Refetch data after update
      await fetchContent();
      return { error: null };
    } catch (err: any) {
      console.error(`Error updating content for ${sectionKey}:`, err);
      return { error: err.message || 'Failed to update content' };
    }
  };

  const createContent = async (contentData: Omit<ContentSection, 'id' | 'created_at' | 'updated_at'>) => {
    try {
      const { data: newContent, error: createError } = await supabase
        .from('content_sections')
        .insert([{
          ...contentData,
          section_key: sectionKey
        }])
        .select()
        .single();
      
      if (createError) throw createError;
      
      setData(newContent);
      return { data: newContent, error: null };
    } catch (err: any) {
      console.error(`Error creating content for ${sectionKey}:`, err);
      return { data: null, error: err.message || 'Failed to create content' };
    }
  };

  useEffect(() => {
    fetchContent();
  }, [sectionKey]);

  return {
    data,
    loading,
    error,
    updateContent,
    createContent,
    refetch: fetchContent
  };
};

// Hook for multiple content sections
export const useSupabaseContentMultiple = (sectionKeys: string[]) => {
  const [data, setData] = useState<ContentSection[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchMultipleContent = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const { data: contentData, error: fetchError } = await supabase
        .from('content_sections')
        .select('*')
        .in('section_key', sectionKeys)
        .eq('is_active', true);
      
      if (fetchError) throw fetchError;
      
      setData(contentData || []);
    } catch (err: any) {
      console.error('Error fetching multiple content sections:', err);
      setError(err.message || 'Failed to fetch content');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (sectionKeys.length > 0) {
      fetchMultipleContent();
    }
  }, [sectionKeys.join(',')]);

  return {
    data,
    loading,
    error,
    refetch: fetchMultipleContent
  };
};
