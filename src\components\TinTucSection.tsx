import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Calendar, User, Eye, Heart, MessageCircle, ArrowRight, TrendingUp, Clock, Star } from "lucide-react";
import { Link } from "react-router-dom";
import { useNews } from "@/hooks/useNews";
import { useCategories } from "@/hooks/useCategories";
import { format } from "date-fns";
import { vi } from "date-fns/locale";
import { useState } from "react";

const TinTucSection = () => {
  const { articles, loading } = useNews({ published: true });
  const { categories: dbCategories } = useCategories();
  const [selectedCategory, setSelectedCategory] = useState("Tất cả");

  // Format date helper
  const formatDate = (dateString: string) => {
    try {
      return format(new Date(dateString), "dd/MM/yyyy", { locale: vi });
    } catch {
      return dateString;
    }
  };

  // Calculate read time based on content length
  const calculateReadTime = (content: string) => {
    const wordsPerMinute = 200;
    const words = content.split(' ').length;
    const minutes = Math.ceil(words / wordsPerMinute);
    return `${minutes} phút`;
  };

  // Build categories with counts from RPC data
  const categories = [
    { name: "Tất cả", active: selectedCategory === "Tất cả", count: articles.length },
    ...dbCategories.map(cat => ({
      name: cat.name,
      active: selectedCategory === cat.name,
      count: cat.count
    }))
  ];

  // Filter articles based on selected category
  const filteredArticles = selectedCategory === "Tất cả"
    ? articles
    : articles.filter(article => article.category === selectedCategory);

  return (
    <section id="tintuc" className="py-20 bg-gradient-to-br from-secondary/30 via-background to-secondary/20 relative overflow-hidden">
      {/* Background decoration */}
      <div className="absolute inset-0 bg-gradient-to-r from-primary/5 via-transparent to-accent/5"></div>
      <div className="absolute top-20 left-20 w-64 h-64 bg-primary/10 rounded-full blur-3xl"></div>
      <div className="absolute bottom-20 right-20 w-48 h-48 bg-accent/10 rounded-full blur-2xl"></div>

      <div className="container mx-auto px-4 relative z-10">
        <div className="text-center mb-16">
          <div className="inline-flex items-center gap-2 bg-primary/10 text-primary px-4 py-2 rounded-full text-sm font-medium mb-4">
            <TrendingUp className="w-4 h-4" />
            Tin tức mới nhất
          </div>
          <h2 className="text-4xl font-bold text-foreground mb-4">
            TIN TỨC & SỰ KIỆN
          </h2>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
            Cập nhật những tin tức mới nhất về dịch vụ taxi, khuyến mãi hấp dẫn và các sự kiện đặc biệt
          </p>
        </div>

        {/* Category Filter */}
        <div className="flex flex-wrap justify-center gap-3 mb-12">
          {categories.map((category, index) => (
            <Button
              key={index}
              variant={category.active ? "default" : "outline"}
              size="sm"
              onClick={() => setSelectedCategory(category.name)}
              className={`rounded-full transition-all duration-300 ${category.active
                ? "bg-gradient-primary text-white shadow-medium hover:shadow-strong"
                : "border-primary/30 text-primary hover:bg-primary hover:text-white"
                }`}
            >
              {category.name}
              <span className="ml-2 px-2 py-0.5 bg-white/20 rounded-full text-xs">
                {category.count}
              </span>
            </Button>
          ))}
        </div>

        {/* Content */}
        {loading ? (
          <div className="text-center py-12">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
            <p className="mt-2 text-muted-foreground">Đang tải tin tức...</p>
          </div>
        ) : filteredArticles.length > 0 ? (
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-12">
            {/* Main Featured Article */}
            <div className="lg:col-span-2">
              <Link to={`/news/${filteredArticles[0]?.slug || filteredArticles[0]?.id}`}>
                <Card className="group overflow-hidden border-0 bg-white/90 backdrop-blur-sm shadow-medium hover:shadow-strong transition-all duration-500 cursor-pointer">
                  <div className="relative">
                    <div className="aspect-video bg-gradient-to-br from-primary/20 via-primary/10 to-accent/10 flex items-center justify-center relative overflow-hidden">
                      <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent"></div>
                      <div className="text-6xl opacity-20 group-hover:scale-110 transition-transform duration-500">
                        📰
                      </div>
                      <div className="absolute top-3 right-3 bg-white/90 backdrop-blur-sm px-2 py-1 rounded-full text-xs text-muted-foreground">
                        {calculateReadTime(filteredArticles[0]?.content || '')}
                      </div>
                    </div>
                  </div>

                  <CardContent className="p-6">
                    <div className="flex items-center gap-3 mb-4 text-xs">
                      <span className="bg-primary/10 text-primary px-2 py-1 rounded-full font-medium">
                        {filteredArticles[0]?.category}
                      </span>
                      <div className="flex items-center text-xs text-muted-foreground">
                        <Calendar className="w-3 h-3 mr-1" />
                        {formatDate(filteredArticles[0]?.publish_date || '')}
                      </div>
                    </div>

                    <h3 className="text-2xl font-bold text-foreground mb-4 group-hover:text-primary transition-colors leading-tight">
                      {filteredArticles[0]?.title}
                    </h3>

                    <p className="text-muted-foreground mb-6 leading-relaxed line-clamp-3">
                      {filteredArticles[0]?.excerpt}
                    </p>

                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2 text-xs text-muted-foreground">
                        <User className="w-3 h-3" />
                        {filteredArticles[0]?.author}
                      </div>
                      <div className="flex items-center gap-3 text-xs text-muted-foreground">
                        <span className="flex items-center gap-1">
                          <Eye className="w-3 h-3" />
                          {filteredArticles[0]?.views || 0}
                        </span>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </Link>
            </div>

            {/* Side Articles */}
            <div className="space-y-6">
              {filteredArticles.slice(1, 4).map((article, index) => (
                <Link key={article.id} to={`/news/${article.slug || article.id}`}>
                  <Card className="group overflow-hidden border-0 bg-white/80 backdrop-blur-sm shadow-soft hover:shadow-medium transition-all duration-300 cursor-pointer">
                    <CardContent className="p-4">
                      <div className="flex items-start justify-between mb-3">
                        <span className="text-xs text-muted-foreground">
                          {calculateReadTime(article.content || '')}
                        </span>
                        {article.is_featured && (
                          <Star className="w-3 h-3 text-yellow-500 fill-current" />
                        )}
                      </div>

                      <h4 className="font-semibold text-foreground mb-3 group-hover:text-primary transition-colors leading-tight line-clamp-2">
                        {article.title}
                      </h4>

                      <p className="text-sm text-muted-foreground mb-3 line-clamp-2">
                        {article.excerpt}
                      </p>

                      <div className="flex items-center justify-between text-xs text-muted-foreground">
                        <div className="flex items-center">
                          <Calendar className="w-3 h-3 mr-1" />
                          {formatDate(article.publish_date || '')}
                        </div>
                        <span className="bg-primary/10 text-primary px-2 py-1 rounded-full text-xs">
                          {article.category}
                        </span>
                      </div>
                    </CardContent>
                  </Card>
                </Link>
              ))}
            </div>
          </div>
        ) : (
          <div className="text-center py-12">
            <div className="text-6xl mb-4">📰</div>
            <h3 className="text-xl font-semibold text-foreground mb-2">Chưa có tin tức</h3>
            <p className="text-muted-foreground">Hãy quay lại sau để xem những tin tức mới nhất!</p>
          </div>
        )}

        {/* Call to Action */}
        <div className="text-center">
          <Link to="/news">
            <Button
              size="lg"
              variant="outline"
              className="border-primary/50 text-primary hover:bg-gradient-primary hover:text-white hover:border-transparent shadow-medium hover:shadow-strong transition-all duration-300 px-8 py-3 rounded-xl group"
            >
              <TrendingUp className="w-5 h-5 mr-2 group-hover:animate-bounce" />
              Xem tất cả tin tức
              <ArrowRight className="w-5 h-5 ml-2 group-hover:translate-x-1 transition-transform" />
            </Button>
          </Link>
        </div>
      </div>
    </section>
  );
};

export default TinTucSection;
