import { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';

export interface Comment {
  id: string;
  article_id: string;
  author_name: string;
  author_email: string;
  content: string;
  likes: number;
  is_approved: boolean;
  created_at: string;
  updated_at: string;
}

export interface NewComment {
  article_id: string;
  author_name: string;
  author_email: string;
  content: string;
}

export const useComments = (articleId?: string) => {
  const [comments, setComments] = useState<Comment[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchComments = async () => {
    if (!articleId) {
      setComments([]);
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      setError(null);
      
      const { data, error: fetchError } = await supabase
        .from('comments')
        .select('*')
        .eq('article_id', articleId)
        .eq('is_approved', true)
        .order('created_at', { ascending: false });
      
      if (fetchError) throw fetchError;
      
      setComments(data || []);
    } catch (err: any) {
      console.error('Error fetching comments:', err);
      setError(err.message || 'Failed to fetch comments');
    } finally {
      setLoading(false);
    }
  };

  const createComment = async (newComment: NewComment) => {
    try {
      const { data, error: createError } = await supabase
        .from('comments')
        .insert([newComment])
        .select()
        .single();
      
      if (createError) throw createError;
      
      // Don't add to local state since it needs approval
      return { data, error: null };
    } catch (err: any) {
      console.error('Error creating comment:', err);
      return { data: null, error: err.message || 'Failed to create comment' };
    }
  };

  const likeComment = async (commentId: string) => {
    try {
      await supabase.rpc('increment_comment_likes', { comment_id: commentId });
      
      // Update local state
      setComments(prev => 
        prev.map(comment => 
          comment.id === commentId 
            ? { ...comment, likes: comment.likes + 1 }
            : comment
        )
      );
      
      return { error: null };
    } catch (err: any) {
      console.error('Error liking comment:', err);
      return { error: err.message || 'Failed to like comment' };
    }
  };

  useEffect(() => {
    fetchComments();
  }, [articleId]);

  return {
    comments,
    loading,
    error,
    fetchComments,
    createComment,
    likeComment,
    refetch: fetchComments
  };
};

// Hook for admin to manage all comments
export const useCommentsAdmin = () => {
  const [comments, setComments] = useState<Comment[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchAllComments = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const { data, error: fetchError } = await supabase
        .from('comments')
        .select(`
          *,
          news_articles (
            title,
            slug
          )
        `)
        .order('created_at', { ascending: false });
      
      if (fetchError) throw fetchError;
      
      setComments(data || []);
    } catch (err: any) {
      console.error('Error fetching all comments:', err);
      setError(err.message || 'Failed to fetch comments');
    } finally {
      setLoading(false);
    }
  };

  const approveComment = async (commentId: string) => {
    try {
      const { error: updateError } = await supabase
        .from('comments')
        .update({ is_approved: true })
        .eq('id', commentId);
      
      if (updateError) throw updateError;
      
      await fetchAllComments(); // Refresh the list
      return { error: null };
    } catch (err: any) {
      console.error('Error approving comment:', err);
      return { error: err.message || 'Failed to approve comment' };
    }
  };

  const deleteComment = async (commentId: string) => {
    try {
      const { error: deleteError } = await supabase
        .from('comments')
        .delete()
        .eq('id', commentId);
      
      if (deleteError) throw deleteError;
      
      await fetchAllComments(); // Refresh the list
      return { error: null };
    } catch (err: any) {
      console.error('Error deleting comment:', err);
      return { error: err.message || 'Failed to delete comment' };
    }
  };

  useEffect(() => {
    fetchAllComments();
  }, []);

  return {
    comments,
    loading,
    error,
    fetchAllComments,
    approveComment,
    deleteComment,
    refetch: fetchAllComments
  };
};
