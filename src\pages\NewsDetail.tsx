import { Card, CardContent } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Calendar, User, ArrowLeft, Eye, Heart, MessageCircle, Clock,
  Share2, Bookmark, ThumbsUp, ChevronLeft, ChevronRight, Tag
} from "lucide-react";
import Header from "@/components/Header";
import Footer from "@/components/Footer";
import FloatingContacts from "@/components/FloatingContacts";
import { Link, useParams } from "react-router-dom";
import { useEffect, useMemo } from "react";
import { useNewsArticle } from "@/hooks/useNews";
import { useRelatedArticles } from "@/hooks/useRelatedArticles";
import { format } from "date-fns";
import { vi } from "date-fns/locale";
import MarkdownRenderer from "@/components/MarkdownRenderer";

const NewsDetail = () => {
  const { id } = useParams();
  const { article, loading, error } = useNewsArticle(id || '');

  // Get related articles from database
  const { relatedArticles } = useRelatedArticles(article?.id, article?.category);

  // Scroll to top when component mounts or ID changes
  useEffect(() => {
    window.scrollTo(0, 0);
  }, [id]);

  // Format date helper
  const formatDate = (dateString: string) => {
    try {
      return format(new Date(dateString), "dd/MM/yyyy", { locale: vi });
    } catch {
      return dateString;
    }
  };

  // Calculate read time based on content length
  const calculateReadTime = (content: string) => {
    const wordsPerMinute = 200;
    const words = content.split(' ').length;
    const minutes = Math.ceil(words / wordsPerMinute);
    return `${minutes} phút`;
  };

  if (loading) {
    return (
      <div className="min-h-screen">
        <Header />
        <div className="container mx-auto px-4 py-16">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
            <p className="mt-2 text-muted-foreground">Đang tải bài viết...</p>
          </div>
        </div>
        <Footer />
        <FloatingContacts />
      </div>
    );
  }

  if (error || !article) {
    return (
      <div className="min-h-screen">
        <Header />
        <div className="container mx-auto px-4 py-16">
          <div className="text-center">
            <h1 className="text-2xl font-bold text-foreground mb-4">Không tìm thấy bài viết</h1>
            <p className="text-muted-foreground mb-8">Bài viết bạn đang tìm kiếm không tồn tại hoặc đã bị xóa.</p>
            <Link to="/news">
              <Button>
                <ArrowLeft className="w-4 h-4 mr-2" />
                Quay lại trang tin tức
              </Button>
            </Link>
          </div>
        </div>
        <Footer />
        <FloatingContacts />
      </div>
    );
  }



  return (
    <div className="min-h-screen">
      <Header />

      {/* Breadcrumb */}
      <div className="bg-secondary/30 py-4">
        <div className="container mx-auto px-4">
          <div className="flex items-center gap-2 text-sm text-muted-foreground">
            <Link to="/" className="hover:text-primary">Trang chủ</Link>
            <ChevronRight className="w-4 h-4" />
            <Link to="/news" className="hover:text-primary">Tin tức</Link>
            <ChevronRight className="w-4 h-4" />
            <span className="text-foreground">Chi tiết</span>
          </div>
        </div>
      </div>

      <div className="container mx-auto px-4 py-12">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-12">
          {/* Main Content */}
          <div className="lg:col-span-2">
            {/* Back Button */}
            <Link to="/news">
              <Button variant="ghost" className="mb-6 text-primary hover:bg-primary/10">
                <ArrowLeft className="w-4 h-4 mr-2" />
                Quay lại danh sách
              </Button>
            </Link>

            {/* Article Header */}
            <Card className="border-0 shadow-medium mb-8">
              <div className="aspect-video bg-gradient-to-br from-primary/20 via-primary/10 to-accent/10 flex items-center justify-center relative overflow-hidden">
                <div className="text-8xl">🚗</div>
                <div className="absolute top-4 left-4">
                  <Badge className="bg-gradient-to-r from-primary to-primary-hover text-white">
                    {article.category}
                  </Badge>
                </div>
                <div className="absolute top-4 right-4 bg-white/90 backdrop-blur-sm px-3 py-1 rounded-full text-sm text-muted-foreground">
                  <Clock className="w-4 h-4 inline mr-1" />
                  {calculateReadTime(article.content)}
                </div>
              </div>

              <CardContent className="p-8">
                <h1 className="text-3xl md:text-4xl font-bold text-foreground mb-6 leading-tight">
                  {article.title}
                </h1>

                <div className="flex flex-wrap items-center gap-6 mb-6 text-sm text-muted-foreground">
                  <div className="flex items-center gap-2">
                    <Calendar className="w-4 h-4" />
                    <span>{formatDate(article.publish_date)}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <User className="w-4 h-4" />
                    <span>{article.author}</span>
                  </div>
                  <div className="flex items-center gap-4">
                    <span className="flex items-center gap-1">
                      <Eye className="w-4 h-4" />
                      {article.views}
                    </span>
                  </div>
                </div>

                {/* Action Buttons */}
                <div className="flex flex-wrap gap-3 mb-8">
                  <Button size="sm" variant="outline" className="text-blue-600 border-blue-200 hover:bg-blue-50">
                    <Share2 className="w-4 h-4 mr-2" />
                    Chia sẻ
                  </Button>
                  <Button size="sm" variant="outline" className="text-green-600 border-green-200 hover:bg-green-50">
                    <Bookmark className="w-4 h-4 mr-2" />
                    Lưu bài
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* Article Content */}
            <Card className="border-0 shadow-medium mb-8">
              <CardContent className="p-8">
                <MarkdownRenderer
                  content={article.content || ''}
                  className="prose prose-lg max-w-none text-muted-foreground leading-relaxed"
                />

                {/* Tags - Temporarily hidden until tags field is added to database */}
                {false && (
                  <div className="mt-8 pt-8 border-t">
                    <h4 className="font-semibold text-foreground mb-3">Tags:</h4>
                    <div className="flex flex-wrap gap-2">
                      <Badge variant="secondary" className="text-xs">
                        <Tag className="w-3 h-3 mr-1" />
                        Sample Tag
                      </Badge>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Comments Section */}
            <Card className="border-0 shadow-medium">
              <CardContent className="p-8">
                <h3 className="text-xl font-bold text-foreground mb-6 flex items-center gap-2">
                  <MessageCircle className="w-5 h-5" />
                  Bình luận ()
                </h3>

                {/* Comment Form */}
                <div className="mb-8 p-6 bg-secondary/20 rounded-lg">
                  <h4 className="font-semibold text-foreground mb-4">Để lại bình luận của bạn</h4>
                  <div className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <input
                        type="text"
                        placeholder="Họ và tên"
                        className="px-4 py-2 rounded-lg border border-border focus:outline-none focus:ring-2 focus:ring-primary bg-white"
                      />
                      <input
                        type="email"
                        placeholder="Email"
                        className="px-4 py-2 rounded-lg border border-border focus:outline-none focus:ring-2 focus:ring-primary bg-white"
                      />
                    </div>
                    <textarea
                      placeholder="Nội dung bình luận..."
                      rows={4}
                      className="w-full px-4 py-2 rounded-lg border border-border focus:outline-none focus:ring-2 focus:ring-primary bg-white resize-none"
                    />
                    <Button className="bg-gradient-primary hover:bg-primary-hover">
                      <MessageCircle className="w-4 h-4 mr-2" />
                      Gửi bình luận
                    </Button>
                  </div>
                </div>

                {/* Comments List */}
                <div className="space-y-6">
                  {[
                    {
                      id: 1,
                      name: "Nguyễn Văn A",
                      time: "2 giờ trước",
                      content: "Dịch vụ rất tốt, xe sạch sẽ và tài xế lịch sự. Tôi sẽ sử dụng dịch vụ này thường xuyên.",
                      likes: 5,
                      avatar: "👤"
                    },
                    {
                      id: 2,
                      name: "Trần Thị B",
                      time: "1 ngày trước",
                      content: "Giá cả hợp lý, đặt xe online rất tiện lợi. Tài xế đến đúng giờ và lái xe an toàn.",
                      likes: 3,
                      avatar: "👩"
                    },
                    {
                      id: 3,
                      name: "Lê Minh C",
                      time: "2 ngày trước",
                      content: "Tuyến đường mới này rất thuận tiện cho việc đi du lịch. Xe limousine cao cấp, đáng đồng tiền bát gạo.",
                      likes: 8,
                      avatar: "👨"
                    }
                  ].map((comment) => (
                    <div key={comment.id} className="border-b border-border pb-4 last:border-b-0">
                      <div className="flex items-start gap-4">
                        <div className="w-10 h-10 bg-gradient-to-br from-primary/20 to-accent/20 rounded-full flex items-center justify-center">
                          <span className="text-sm">{comment.avatar}</span>
                        </div>
                        <div className="flex-1">
                          <div className="flex items-center gap-2 mb-2">
                            <span className="font-medium text-foreground">{comment.name}</span>
                            <span className="text-xs text-muted-foreground">{comment.time}</span>
                          </div>
                          <p className="text-muted-foreground mb-3 leading-relaxed">
                            {comment.content}
                          </p>
                          <div className="flex items-center gap-4">
                            <Button variant="ghost" size="sm" className="text-xs text-muted-foreground hover:text-primary">
                              <ThumbsUp className="w-3 h-3 mr-1" />
                              Hữu ích ({comment.likes})
                            </Button>
                            <Button variant="ghost" size="sm" className="text-xs text-muted-foreground hover:text-primary">
                              Trả lời
                            </Button>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Sidebar */}
          <div className="lg:col-span-1">
            {/* Related Articles */}
            <Card className="border-0 shadow-medium mb-8">
              <CardContent className="p-6">
                <h3 className="text-lg font-bold text-foreground mb-4">Bài viết liên quan</h3>
                <div className="space-y-4">
                  {relatedArticles.map((related) => (
                    <Link key={related.id} to={`/news/${related.slug || related.id}`}>
                      <div className="group border-b border-border pb-4 last:border-b-0 hover:bg-secondary/30 p-2 rounded transition-colors">
                        <Badge variant="secondary" className="text-xs mb-2">{related.category}</Badge>
                        <h4 className="font-medium text-foreground group-hover:text-primary transition-colors line-clamp-2 mb-2">
                          {related.title}
                        </h4>
                        <p className="text-sm text-muted-foreground line-clamp-2 mb-2">
                          {related.excerpt}
                        </p>
                        <div className="flex items-center justify-between text-xs text-muted-foreground">
                          <span>{related.date}</span>
                          <span>{related.readTime}</span>
                        </div>
                      </div>
                    </Link>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Newsletter Signup */}
            <Card className="border-0 shadow-medium bg-gradient-to-br from-primary/10 to-accent/10">
              <CardContent className="p-6 text-center">
                <h3 className="text-lg font-bold text-foreground mb-3">Đăng ký nhận tin</h3>
                <p className="text-sm text-muted-foreground mb-4">
                  Nhận thông báo về các tin tức mới nhất từ Vân Đăm Tour
                </p>
                <div className="space-y-3">
                  <input
                    type="email"
                    placeholder="Email của bạn"
                    className="w-full px-3 py-2 rounded border border-border focus:outline-none focus:ring-2 focus:ring-primary text-sm"
                  />
                  <Button className="w-full bg-gradient-primary hover:bg-primary-hover text-sm">
                    Đăng ký ngay
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>

      <Footer />
      <FloatingContacts />
    </div>
  );
};

export default NewsDetail;