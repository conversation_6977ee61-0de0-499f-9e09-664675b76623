import { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';

export interface PricingPlan {
  id: string;
  plan_id: string;
  title: string;
  description: string;
  icon_type: string;
  is_popular: boolean;
  prices: Array<{
    distance: string;
    price: string;
    unit: string;
  }>;
  features: string[];
  is_active: boolean;
  display_order: number;
  created_at: string;
  updated_at: string;
}

export const usePricing = () => {
  const [plans, setPlans] = useState<PricingPlan[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchPricing = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const { data, error: fetchError } = await supabase
        .from('pricing_plans')
        .select('*')
        .eq('is_active', true)
        .order('display_order', { ascending: true });
      
      if (fetchError) throw fetchError;
      
      setPlans(data || []);
    } catch (err: any) {
      console.error('Error fetching pricing plans:', err);
      setError(err.message || 'Failed to fetch pricing plans');
    } finally {
      setLoading(false);
    }
  };

  const createPlan = async (planData: Omit<PricingPlan, 'id' | 'created_at' | 'updated_at'>) => {
    try {
      const { data, error: createError } = await supabase
        .from('pricing_plans')
        .insert([planData])
        .select()
        .single();
      
      if (createError) throw createError;
      
      await fetchPricing(); // Refresh the list
      return { data, error: null };
    } catch (err: any) {
      console.error('Error creating pricing plan:', err);
      return { data: null, error: err.message || 'Failed to create pricing plan' };
    }
  };

  const updatePlan = async (id: string, updates: Partial<PricingPlan>) => {
    try {
      const { error: updateError } = await supabase
        .from('pricing_plans')
        .update({
          ...updates,
          updated_at: new Date().toISOString()
        })
        .eq('id', id);
      
      if (updateError) throw updateError;
      
      await fetchPricing(); // Refresh the list
      return { error: null };
    } catch (err: any) {
      console.error('Error updating pricing plan:', err);
      return { error: err.message || 'Failed to update pricing plan' };
    }
  };

  const deletePlan = async (id: string) => {
    try {
      // Soft delete by setting is_active to false
      const { error: deleteError } = await supabase
        .from('pricing_plans')
        .update({ 
          is_active: false,
          updated_at: new Date().toISOString()
        })
        .eq('id', id);
      
      if (deleteError) throw deleteError;
      
      await fetchPricing(); // Refresh the list
      return { error: null };
    } catch (err: any) {
      console.error('Error deleting pricing plan:', err);
      return { error: err.message || 'Failed to delete pricing plan' };
    }
  };

  const reorderPlans = async (planIds: string[]) => {
    try {
      // Update display_order for each plan
      const updates = planIds.map((id, index) => 
        supabase
          .from('pricing_plans')
          .update({ 
            display_order: index + 1,
            updated_at: new Date().toISOString()
          })
          .eq('id', id)
      );
      
      await Promise.all(updates);
      await fetchPricing(); // Refresh the list
      return { error: null };
    } catch (err: any) {
      console.error('Error reordering pricing plans:', err);
      return { error: err.message || 'Failed to reorder pricing plans' };
    }
  };

  const togglePopular = async (id: string) => {
    try {
      // First, set all plans to not popular
      await supabase
        .from('pricing_plans')
        .update({ is_popular: false });
      
      // Then set the selected plan as popular
      const { error: updateError } = await supabase
        .from('pricing_plans')
        .update({ 
          is_popular: true,
          updated_at: new Date().toISOString()
        })
        .eq('id', id);
      
      if (updateError) throw updateError;
      
      await fetchPricing(); // Refresh the list
      return { error: null };
    } catch (err: any) {
      console.error('Error toggling popular plan:', err);
      return { error: err.message || 'Failed to toggle popular plan' };
    }
  };

  useEffect(() => {
    fetchPricing();
  }, []);

  return {
    plans,
    loading,
    error,
    fetchPricing,
    createPlan,
    updatePlan,
    deletePlan,
    reorderPlans,
    togglePopular,
    refetch: fetchPricing
  };
};

// Hook for getting a single pricing plan by plan_id
export const usePricingPlan = (planId: string) => {
  const [plan, setPlan] = useState<PricingPlan | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchPlan = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const { data, error: fetchError } = await supabase
        .from('pricing_plans')
        .select('*')
        .eq('plan_id', planId)
        .eq('is_active', true)
        .single();
      
      if (fetchError) {
        if (fetchError.code === 'PGRST116') {
          setPlan(null);
        } else {
          throw fetchError;
        }
      } else {
        setPlan(data);
      }
    } catch (err: any) {
      console.error(`Error fetching pricing plan ${planId}:`, err);
      setError(err.message || 'Failed to fetch pricing plan');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (planId) {
      fetchPlan();
    }
  }, [planId]);

  return {
    plan,
    loading,
    error,
    refetch: fetchPlan
  };
};
