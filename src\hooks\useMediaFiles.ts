import { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';

export interface MediaFile {
  id: string;
  filename: string;
  original_name: string;
  file_path: string;
  file_size: number;
  mime_type: string;
  category: string;
  alt_text?: string;
  is_active: boolean;
  uploaded_by?: string;
  created_at: string;
}

export interface MediaFileInput {
  filename: string;
  original_name: string;
  file_path: string;
  file_size: number;
  mime_type: string;
  category: string;
  alt_text?: string;
  is_active?: boolean;
  uploaded_by?: string;
}

export const useMediaFiles = (category?: string) => {
  const [files, setFiles] = useState<MediaFile[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchFiles = async () => {
    try {
      setLoading(true);
      let query = supabase
        .from('media_files')
        .select('*')
        .eq('is_active', true)
        .order('created_at', { ascending: false });

      if (category) {
        query = query.eq('category', category);
      }

      const { data, error } = await query;

      if (error) {
        setError(error.message);
        return;
      }

      setFiles(data || []);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchFiles();
  }, [category]);

  const uploadFile = async (fileInput: MediaFileInput) => {
    try {
      const { data, error } = await supabase
        .from('media_files')
        .insert([fileInput])
        .select()
        .single();

      if (error) {
        return { data: null, error: error.message };
      }

      // Refresh the files list
      await fetchFiles();
      return { data, error: null };
    } catch (err) {
      return { 
        data: null, 
        error: err instanceof Error ? err.message : 'An error occurred' 
      };
    }
  };

  const updateFile = async (id: string, updates: Partial<MediaFileInput>) => {
    try {
      const { data, error } = await supabase
        .from('media_files')
        .update(updates)
        .eq('id', id)
        .select()
        .single();

      if (error) {
        return { data: null, error: error.message };
      }

      // Refresh the files list
      await fetchFiles();
      return { data, error: null };
    } catch (err) {
      return { 
        data: null, 
        error: err instanceof Error ? err.message : 'An error occurred' 
      };
    }
  };

  const deleteFile = async (id: string) => {
    try {
      const { error } = await supabase
        .from('media_files')
        .update({ is_active: false })
        .eq('id', id);

      if (error) {
        return { error: error.message };
      }

      // Refresh the files list
      await fetchFiles();
      return { error: null };
    } catch (err) {
      return { 
        error: err instanceof Error ? err.message : 'An error occurred' 
      };
    }
  };

  const getFilesByCategory = (categoryFilter: string) => {
    return files.filter(file => file.category === categoryFilter);
  };

  const getFileUrl = (filePath: string) => {
    // For now, return the file path as is
    // In production, this would generate a signed URL from Supabase Storage
    return filePath;
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const getFileType = (mimeType: string) => {
    if (mimeType.startsWith('image/')) return 'image';
    if (mimeType.startsWith('video/')) return 'video';
    if (mimeType.startsWith('audio/')) return 'audio';
    if (mimeType.includes('pdf')) return 'pdf';
    if (mimeType.includes('document') || mimeType.includes('word')) return 'document';
    if (mimeType.includes('spreadsheet') || mimeType.includes('excel')) return 'spreadsheet';
    return 'file';
  };

  const getCategories = () => {
    const categories = [...new Set(files.map(file => file.category))];
    return categories.sort();
  };

  return {
    files,
    loading,
    error,
    uploadFile,
    updateFile,
    deleteFile,
    getFilesByCategory,
    getFileUrl,
    formatFileSize,
    getFileType,
    getCategories,
    refetch: fetchFiles
  };
};

export const useMediaFile = (id: string) => {
  const [file, setFile] = useState<MediaFile | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchFile = async () => {
      try {
        setLoading(true);
        const { data, error } = await supabase
          .from('media_files')
          .select('*')
          .eq('id', id)
          .eq('is_active', true)
          .single();

        if (error) {
          setError(error.message);
          return;
        }

        setFile(data);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'An error occurred');
      } finally {
        setLoading(false);
      }
    };

    if (id) {
      fetchFile();
    }
  }, [id]);

  return { file, loading, error };
};

// Hook for media file statistics
export const useMediaStats = () => {
  const [stats, setStats] = useState({
    totalFiles: 0,
    totalSize: 0,
    byCategory: {} as Record<string, number>,
    byType: {} as Record<string, number>
  });
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchStats = async () => {
      try {
        setLoading(true);
        const { data, error } = await supabase
          .from('media_files')
          .select('file_size, category, mime_type')
          .eq('is_active', true);

        if (error) {
          console.error('Error fetching media stats:', error);
          return;
        }

        const totalFiles = data.length;
        const totalSize = data.reduce((sum, file) => sum + (file.file_size || 0), 0);
        
        const byCategory: Record<string, number> = {};
        const byType: Record<string, number> = {};

        data.forEach(file => {
          // Count by category
          byCategory[file.category] = (byCategory[file.category] || 0) + 1;
          
          // Count by type
          const type = file.mime_type.startsWith('image/') ? 'image' :
                      file.mime_type.startsWith('video/') ? 'video' :
                      file.mime_type.startsWith('audio/') ? 'audio' : 'other';
          byType[type] = (byType[type] || 0) + 1;
        });

        setStats({
          totalFiles,
          totalSize,
          byCategory,
          byType
        });
      } catch (err) {
        console.error('Error fetching media stats:', err);
      } finally {
        setLoading(false);
      }
    };

    fetchStats();
  }, []);

  return { stats, loading };
};
