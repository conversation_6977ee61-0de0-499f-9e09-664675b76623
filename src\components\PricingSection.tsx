import { <PERSON>, CardContent, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Check, Car, Users } from "lucide-react";
import { useSupabaseContent } from "@/hooks/useSupabaseContent";

const PricingSection = () => {
  const { data: pricingConfig, loading } = useSupabaseContent('pricing');

  // Extract plans from content_sections data only
  const plans = pricingConfig?.content?.plans || [];

  const scrollToBooking = () => {
    // Try multiple possible booking form IDs
    const bookingSection = document.getElementById('booking-form') ||
      document.getElementById('datxe') ||
      document.querySelector('[data-booking-form]') ||
      document.querySelector('.booking-form');
    if (bookingSection) {
      bookingSection.scrollIntoView({ behavior: 'smooth' });
    } else {
      // Fallback: scroll to top of page
      window.scrollTo({ top: 0, behavior: 'smooth' });
    }
  };

  const getIcon = (iconType: string) => {
    switch (iconType) {
      case 'car':
        return <Car className="w-6 h-6" />;
      case 'users':
        return <Users className="w-6 h-6" />;
      default:
        return <Car className="w-6 h-6" />;
    }
  };

  return (
    <section id="banggia" className="py-20 bg-gradient-to-br from-background via-background to-primary/5">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <h2 className="text-4xl font-bold text-foreground mb-4">
            {pricingConfig?.content?.sectionTitle || "BẢNG GIÁ DỊCH VỤ"}
          </h2>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
            {pricingConfig?.content?.sectionDescription || "Bảng giá minh bạch, cạnh tranh cho tất cả các dịch vụ taxi của chúng tôi"}
          </p>
        </div>

        {loading ? (
          <div className="text-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
            <p className="mt-2 text-muted-foreground">Đang tải bảng giá...</p>
          </div>
        ) : (
          <>
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 max-w-5xl mx-auto">
              {plans.map((plan, index) => {
                // Parse price string into array
                const priceLines = plan.price?.split('\n') || [];
                const prices = priceLines.map(line => {
                  const parts = line.split(':');
                  if (parts.length === 2) {
                    const distance = parts[0].trim();
                    const priceUnit = parts[1].trim();
                    const priceParts = priceUnit.match(/(\d+k)\/(.+)/);
                    return {
                      distance,
                      price: priceParts ? priceParts[1] : priceUnit,
                      unit: priceParts ? `/${priceParts[2]}` : ''
                    };
                  }
                  return null;
                }).filter(Boolean);

                // Parse features - handle both string and array
                const features = Array.isArray(plan.features)
                  ? plan.features
                  : (typeof plan.features === 'string' ? plan.features.split('\n') : []);

                return (
                  <Card
                    key={plan.id}
                    className={`relative border-2 transition-all duration-300 hover:shadow-strong ${plan.isPopular
                      ? "border-primary shadow-medium"
                      : "border-border hover:border-primary/50"
                      }`}
                    style={{ animationDelay: `${index * 200}ms` }}
                  >
                    {plan.isPopular && (
                      <Badge className="absolute -top-3 left-1/2 transform -translate-x-1/2 bg-primary text-white px-4 py-1">
                        Phổ biến nhất
                      </Badge>
                    )}

                    <CardHeader className="text-center pb-4">
                      <div className="flex items-center justify-center mb-3">
                        <div className={`w-12 h-12 rounded-full flex items-center justify-center ${plan.isPopular ? "bg-primary text-white" : "bg-primary/10 text-primary"
                          }`}>
                          <Car className="w-6 h-6" />
                        </div>
                      </div>
                      <CardTitle className="text-xl font-bold text-primary">
                        {plan.name}
                      </CardTitle>
                    </CardHeader>

                    <CardContent className="space-y-6">
                      {/* Pricing Table */}
                      <div className="space-y-3">
                        {prices.map((priceItem: any, priceIndex: number) => (
                          <div
                            key={priceIndex}
                            className="flex items-center justify-between p-3 bg-secondary/50 rounded-lg"
                          >
                            <span className="text-sm font-medium text-foreground">
                              {priceItem.distance}
                            </span>
                            <div className="text-right">
                              <span className="text-lg font-bold text-primary">
                                {priceItem.price}
                              </span>
                              <span className="text-sm text-muted-foreground ml-1">
                                {priceItem.unit}
                              </span>
                            </div>
                          </div>
                        ))}
                      </div>

                      {/* Features List */}
                      <ul className="space-y-3">
                        {features.map((feature: string, featureIndex: number) => (
                          <li key={featureIndex} className="flex items-start gap-3">
                            <Check className="w-5 h-5 text-primary flex-shrink-0 mt-0.5" />
                            <span className="text-sm text-muted-foreground">{feature}</span>
                          </li>
                        ))}
                      </ul>

                      {/* CTA Button */}
                      <Button
                        onClick={scrollToBooking}
                        className={`w-full py-6 text-lg transition-all duration-300 ${plan.isPopular
                          ? "bg-gradient-primary hover:bg-primary-hover shadow-medium hover:shadow-strong"
                          : "bg-white border-2 border-primary text-primary hover:bg-primary hover:text-white"
                          }`}
                        variant={plan.isPopular ? "default" : "outline"}
                      >
                        Đặt xe ngay
                      </Button>
                    </CardContent>
                  </Card>
                );
              })}
            </div>

            {/* Additional Info */}
            <div className="mt-12 text-center">
              <div className="bg-primary/5 rounded-xl p-6 max-w-4xl mx-auto">
                <h3 className="text-xl font-bold text-foreground mb-3">
                  Chính sách giá ưu đãi
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-muted-foreground">
                  <div className="flex items-center justify-center gap-2">
                    <Check className="w-4 h-4 text-primary" />
                    <span>Giảm 70% lượt về cho khứ hồi</span>
                  </div>
                  <div className="flex items-center justify-center gap-2">
                    <Check className="w-4 h-4 text-primary" />
                    <span>Miễn phí chờ đợi 15 phút đầu</span>
                  </div>
                  <div className="flex items-center justify-center gap-2">
                    <Check className="w-4 h-4 text-primary" />
                    <span>Hỗ trợ 24/7, không ngày lễ</span>
                  </div>
                </div>
              </div>
            </div>
          </>
        )}
      </div>
    </section>
  );
};

export default PricingSection;
