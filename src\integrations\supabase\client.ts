// This file is automatically generated. Do not edit it directly.
import { createClient } from "@supabase/supabase-js";
import type { Database } from "./types";

const SUPABASE_URL = "https://vbtnbpayhgkiyvruznik.supabase.co";
const SUPABASE_PUBLISHABLE_KEY =
  "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InZidG5icGF5aGdraXl2cnV6bmlrIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTQ0NzkxMTAsImV4cCI6MjA3MDA1NTExMH0.NSn4QQdan6yWJyKdRrUeotgl-gq51bjr8R5oyrD6e1I";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(
  SUPABASE_URL,
  SUPABASE_PUBLISHABLE_KEY,
  {
    auth: {
      storage: localStorage,
      persistSession: true,
      autoRefreshToken: true,
    },
  }
);
