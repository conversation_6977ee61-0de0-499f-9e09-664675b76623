-- Create comments table for news articles
CREATE TABLE IF NOT EXISTS comments (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  article_id UUID NOT NULL REFERENCES news_articles(id) ON DELETE CASCADE,
  author_name VA<PERSON>HA<PERSON>(255) NOT NULL,
  author_email VARCHAR(255) NOT NULL,
  content TEXT NOT NULL,
  likes INTEGER DEFAULT 0,
  is_approved BOOLEAN DEFAULT false,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL
);

-- <PERSON>reate indexes for better performance
CREATE INDEX IF NOT EXISTS idx_comments_article_id ON comments(article_id);
CREATE INDEX IF NOT EXISTS idx_comments_approved ON comments(is_approved);
CREATE INDEX IF NOT EXISTS idx_comments_created_at ON comments(created_at);

-- Create RLS policies
ALTER TABLE comments ENABLE ROW LEVEL SECURITY;

-- Allow public to read approved comments
CREATE POLICY "Allow public to read approved comments" ON comments
  FOR SELECT USING (is_approved = true);

-- Allow public to insert comments (they will need approval)
CREATE POLICY "Allow public to insert comments" ON comments
  FOR INSERT WITH CHECK (true);

-- Allow authenticated users to manage all comments (for admin)
CREATE POLICY "Allow authenticated users to manage comments" ON comments
  FOR ALL USING (auth.role() = 'authenticated');

-- Insert some sample comments for existing articles
INSERT INTO comments (article_id, author_name, author_email, content, likes, is_approved) 
SELECT 
  na.id,
  'Nguyễn Văn A',
  '<EMAIL>',
  'Dịch vụ rất tốt, xe sạch sẽ và tài xế lịch sự. Tôi sẽ sử dụng dịch vụ này thường xuyên.',
  5,
  true
FROM news_articles na 
WHERE na.title LIKE '%Khai trương%'
LIMIT 1;

INSERT INTO comments (article_id, author_name, author_email, content, likes, is_approved) 
SELECT 
  na.id,
  'Trần Thị B',
  '<EMAIL>',
  'Giá cả hợp lý, đặt xe online rất tiện lợi. Tài xế đến đúng giờ và lái xe an toàn.',
  3,
  true
FROM news_articles na 
WHERE na.title LIKE '%Ưu đãi%'
LIMIT 1;

INSERT INTO comments (article_id, author_name, author_email, content, likes, is_approved) 
SELECT 
  na.id,
  'Lê Minh C',
  '<EMAIL>',
  'Chương trình khuyến mãi rất hấp dẫn. Tôi đã đặt xe và được giảm giá như quảng cáo.',
  8,
  true
FROM news_articles na 
WHERE na.title LIKE '%Ưu đãi%'
LIMIT 1;

-- Function to increment comment likes
CREATE OR REPLACE FUNCTION increment_comment_likes(comment_id UUID)
RETURNS void AS $$
BEGIN
  UPDATE comments 
  SET likes = likes + 1,
      updated_at = timezone('utc'::text, now())
  WHERE id = comment_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
