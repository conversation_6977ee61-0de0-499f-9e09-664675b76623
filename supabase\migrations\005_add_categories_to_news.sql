-- Add a categories field to news_articles table to store available categories
-- This is a workaround since we can't create new tables from client-side

-- Add a JSON field to store available categories
ALTER TABLE news_articles 
ADD COLUMN IF NOT EXISTS available_categories JSONB DEFAULT '[]'::jsonb;

-- Create an index for better performance
CREATE INDEX IF NOT EXISTS idx_news_articles_available_categories 
ON news_articles USING GIN (available_categories);

-- Insert default categories into the first article
UPDATE news_articles 
SET available_categories = '["Khuyến mãi", "Tin mới", "Hướng dẫn", "Thông báo"]'::jsonb
WHERE id = (SELECT id FROM news_articles LIMIT 1)
AND available_categories = '[]'::jsonb;

-- Function to get all available categories
CREATE OR REPLACE FUNCTION get_available_categories()
RETURNS JSONB AS $$
DECLARE
  categories JSONB;
BEGIN
  -- Get categories from the first article that has them
  SELECT available_categories INTO categories
  FROM news_articles 
  WHERE available_categories != '[]'::jsonb
  LIMIT 1;
  
  -- If no categories found, return default ones
  IF categories IS NULL THEN
    categories := '["Khuyến mãi", "Tin mới"]'::jsonb;
  END IF;
  
  RETURN categories;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to add a new category
CREATE OR REPLACE FUNCTION add_category(category_name TEXT)
RETURNS JSONB AS $$
DECLARE
  current_categories JSONB;
  updated_categories JSONB;
BEGIN
  -- Get current categories
  SELECT get_available_categories() INTO current_categories;
  
  -- Check if category already exists
  IF current_categories ? category_name THEN
    RAISE EXCEPTION 'Category already exists: %', category_name;
  END IF;
  
  -- Add new category
  updated_categories := current_categories || jsonb_build_array(category_name);
  
  -- Update the first article with new categories
  UPDATE news_articles 
  SET available_categories = updated_categories
  WHERE id = (SELECT id FROM news_articles LIMIT 1);
  
  RETURN updated_categories;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to remove a category
CREATE OR REPLACE FUNCTION remove_category(category_name TEXT)
RETURNS JSONB AS $$
DECLARE
  current_categories JSONB;
  updated_categories JSONB;
  articles_count INTEGER;
BEGIN
  -- Check if any articles use this category
  SELECT COUNT(*) INTO articles_count
  FROM news_articles 
  WHERE category = category_name;
  
  IF articles_count > 0 THEN
    RAISE EXCEPTION 'Cannot delete category "%" because % articles are using it', category_name, articles_count;
  END IF;
  
  -- Get current categories
  SELECT get_available_categories() INTO current_categories;
  
  -- Remove category
  updated_categories := current_categories - category_name;
  
  -- Update the first article with new categories
  UPDATE news_articles 
  SET available_categories = updated_categories
  WHERE id = (SELECT id FROM news_articles LIMIT 1);
  
  RETURN updated_categories;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
