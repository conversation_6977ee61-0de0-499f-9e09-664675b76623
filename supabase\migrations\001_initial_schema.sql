-- Migration: Initial Database Schema for BICOM Taxi
-- Created: 2024-12-16
-- Description: Create all tables, indexes, RLS policies, and functions

-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- =============================================
-- TABLE: content_sections
-- Purpose: Manage dynamic content sections
-- =============================================
CREATE TABLE content_sections (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  section_key VARCHAR(100) UNIQUE NOT NULL,
  title VARCHAR(500),
  description TEXT,
  content JSONB,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes for content_sections
CREATE INDEX idx_content_sections_key ON content_sections(section_key);
CREATE INDEX idx_content_sections_active ON content_sections(is_active);

-- =============================================
-- TABLE: services
-- Purpose: Manage taxi services
-- =============================================
CREATE TABLE services (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  service_id VARCHAR(100) UNIQUE NOT NULL,
  title VARCHAR(200) NOT NULL,
  description TEXT,
  icon_url VARCHAR(500),
  features JSONB,
  is_active BOOLEAN DEFAULT true,
  display_order INTEGER DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes for services
CREATE INDEX idx_services_active ON services(is_active);
CREATE INDEX idx_services_order ON services(display_order);
CREATE INDEX idx_services_service_id ON services(service_id);

-- =============================================
-- TABLE: pricing_plans
-- Purpose: Manage pricing plans
-- =============================================
CREATE TABLE pricing_plans (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  plan_id VARCHAR(100) UNIQUE NOT NULL,
  title VARCHAR(200) NOT NULL,
  description TEXT,
  icon_type VARCHAR(50),
  is_popular BOOLEAN DEFAULT false,
  prices JSONB,
  features JSONB,
  is_active BOOLEAN DEFAULT true,
  display_order INTEGER DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes for pricing_plans
CREATE INDEX idx_pricing_plans_active ON pricing_plans(is_active);
CREATE INDEX idx_pricing_plans_order ON pricing_plans(display_order);
CREATE INDEX idx_pricing_plans_plan_id ON pricing_plans(plan_id);

-- =============================================
-- TABLE: news_articles
-- Purpose: Manage news articles
-- =============================================
CREATE TABLE news_articles (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  title VARCHAR(500) NOT NULL,
  slug VARCHAR(500) UNIQUE NOT NULL,
  excerpt TEXT,
  content TEXT,
  author VARCHAR(200),
  category VARCHAR(100),
  featured_image VARCHAR(500),
  is_featured BOOLEAN DEFAULT false,
  is_published BOOLEAN DEFAULT false,
  views INTEGER DEFAULT 0,
  publish_date TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes for news_articles
CREATE INDEX idx_news_published ON news_articles(is_published);
CREATE INDEX idx_news_featured ON news_articles(is_featured);
CREATE INDEX idx_news_category ON news_articles(category);
CREATE INDEX idx_news_slug ON news_articles(slug);
CREATE INDEX idx_news_publish_date ON news_articles(publish_date DESC);

-- =============================================
-- TABLE: bookings
-- Purpose: Manage taxi bookings
-- =============================================
CREATE TABLE bookings (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  booking_code VARCHAR(20) UNIQUE NOT NULL,
  customer_name VARCHAR(200) NOT NULL,
  customer_phone VARCHAR(20) NOT NULL,
  customer_email VARCHAR(200),
  pickup_location TEXT NOT NULL,
  destination TEXT NOT NULL,
  pickup_date DATE NOT NULL,
  pickup_time TIME NOT NULL,
  vehicle_type VARCHAR(50),
  passenger_count INTEGER DEFAULT 1,
  notes TEXT,
  status VARCHAR(50) DEFAULT 'pending',
  estimated_price DECIMAL(10,2),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes for bookings
CREATE INDEX idx_bookings_status ON bookings(status);
CREATE INDEX idx_bookings_date ON bookings(pickup_date);
CREATE INDEX idx_bookings_phone ON bookings(customer_phone);
CREATE INDEX idx_bookings_code ON bookings(booking_code);
CREATE INDEX idx_bookings_created ON bookings(created_at DESC);

-- =============================================
-- TABLE: media_files
-- Purpose: Manage uploaded media files
-- =============================================
CREATE TABLE media_files (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  filename VARCHAR(255) NOT NULL,
  original_name VARCHAR(255) NOT NULL,
  file_path VARCHAR(500) NOT NULL,
  file_size INTEGER,
  mime_type VARCHAR(100),
  category VARCHAR(100),
  alt_text VARCHAR(500),
  is_active BOOLEAN DEFAULT true,
  uploaded_by UUID,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes for media_files
CREATE INDEX idx_media_files_active ON media_files(is_active);
CREATE INDEX idx_media_files_category ON media_files(category);
CREATE INDEX idx_media_files_created ON media_files(created_at DESC);

-- =============================================
-- FUNCTIONS
-- =============================================

-- Function to increment news views
CREATE OR REPLACE FUNCTION increment_news_views(article_id UUID)
RETURNS void AS $$
BEGIN
  UPDATE news_articles 
  SET views = views + 1 
  WHERE id = article_id;
END;
$$ LANGUAGE plpgsql;

-- Function to generate booking code
CREATE OR REPLACE FUNCTION generate_booking_code()
RETURNS TEXT AS $$
BEGIN
  RETURN 'BK' || TO_CHAR(NOW(), 'YYMMDD') || LPAD(FLOOR(RANDOM() * 1000)::TEXT, 3, '0');
END;
$$ LANGUAGE plpgsql;

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- =============================================
-- TRIGGERS
-- =============================================

-- Triggers for updated_at
CREATE TRIGGER update_content_sections_updated_at
  BEFORE UPDATE ON content_sections
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_services_updated_at
  BEFORE UPDATE ON services
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_pricing_plans_updated_at
  BEFORE UPDATE ON pricing_plans
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_news_articles_updated_at
  BEFORE UPDATE ON news_articles
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_bookings_updated_at
  BEFORE UPDATE ON bookings
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- =============================================
-- ROW LEVEL SECURITY (RLS)
-- =============================================

-- Enable RLS on all tables
ALTER TABLE content_sections ENABLE ROW LEVEL SECURITY;
ALTER TABLE services ENABLE ROW LEVEL SECURITY;
ALTER TABLE pricing_plans ENABLE ROW LEVEL SECURITY;
ALTER TABLE news_articles ENABLE ROW LEVEL SECURITY;
ALTER TABLE bookings ENABLE ROW LEVEL SECURITY;
ALTER TABLE media_files ENABLE ROW LEVEL SECURITY;

-- Public read access for active content
CREATE POLICY "Public can read active content" ON content_sections
  FOR SELECT USING (is_active = true);

CREATE POLICY "Public can read active services" ON services
  FOR SELECT USING (is_active = true);

CREATE POLICY "Public can read active pricing" ON pricing_plans
  FOR SELECT USING (is_active = true);

CREATE POLICY "Public can read published news" ON news_articles
  FOR SELECT USING (is_published = true);

CREATE POLICY "Public can read active media" ON media_files
  FOR SELECT USING (is_active = true);

-- Anyone can create bookings
CREATE POLICY "Anyone can create bookings" ON bookings
  FOR INSERT WITH CHECK (true);

-- Admin full access (authenticated users)
CREATE POLICY "Authenticated users full access content" ON content_sections
  FOR ALL USING (auth.role() = 'authenticated');

CREATE POLICY "Authenticated users full access services" ON services
  FOR ALL USING (auth.role() = 'authenticated');

CREATE POLICY "Authenticated users full access pricing" ON pricing_plans
  FOR ALL USING (auth.role() = 'authenticated');

CREATE POLICY "Authenticated users full access news" ON news_articles
  FOR ALL USING (auth.role() = 'authenticated');

CREATE POLICY "Authenticated users full access bookings" ON bookings
  FOR ALL USING (auth.role() = 'authenticated');

CREATE POLICY "Authenticated users full access media" ON media_files
  FOR ALL USING (auth.role() = 'authenticated');
